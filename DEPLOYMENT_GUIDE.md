# Army Equipment Inventory System - IT Deployment Guide

## Overview

This guide provides IT administrators with comprehensive instructions for deploying the Army Equipment Inventory System across enterprise environments. The application is packaged as a standalone executable requiring no Python installation.

## Pre-Deployment Checklist

### System Requirements Verification
- [ ] Target systems meet minimum requirements (Windows 7+ / macOS 10.14+)
- [ ] Sufficient disk space available (200MB minimum)
- [ ] Network connectivity for initial download
- [ ] Administrative privileges for installation

### Security Preparation
- [ ] Antivirus whitelist configured (see ANTIVIRUS_WHITELIST_GUIDE.md)
- [ ] Firewall rules reviewed (application doesn't require internet access)
- [ ] Code signing verification completed
- [ ] Security team approval obtained

### Infrastructure Preparation
- [ ] Distribution method selected (GPO, SCCM, manual, etc.)
- [ ] Network shares configured if needed
- [ ] Backup and rollback procedures defined
- [ ] User communication plan prepared

## Deployment Methods

### Method 1: Group Policy Deployment (Windows)

#### Prerequisites
- Windows Server with Active Directory
- Group Policy Management Console
- Administrative access to domain

#### Steps
1. **Prepare MSI Package**
   ```cmd
   # Convert NSIS installer to MSI if needed
   # Or use provided MSI package
   ```

2. **Create GPO**
   - Open Group Policy Management Console
   - Create new GPO: "Army Inventory System Deployment"
   - Link to appropriate Organizational Unit

3. **Configure Software Installation**
   - Edit GPO → Computer Configuration → Software Settings → Software Installation
   - Right-click → New → Package
   - Browse to MSI file on network share
   - Select "Assigned" deployment method

4. **Configure Antivirus Exclusions**
   - Computer Configuration → Administrative Templates → Windows Components → Microsoft Defender Antivirus
   - Configure path exclusions for application directory

5. **Test and Deploy**
   - Test on pilot group first
   - Monitor deployment through GPMC
   - Gradually expand to all users

### Method 2: SCCM Deployment (Windows)

#### Application Creation
1. **Create Application**
   - Open SCCM Console
   - Software Library → Application Management → Applications
   - Create Application → Manually specify application information

2. **Configure Deployment Type**
   - Installation program: `ArmyInventorySystem_Setup.exe /S`
   - Detection method: File system detection for executable
   - Requirements: Windows 7+ and x64 architecture

3. **Distribute Content**
   - Distribute to distribution points
   - Monitor distribution status

4. **Deploy to Collections**
   - Create device collections for target computers
   - Deploy as "Required" or "Available"
   - Schedule deployment window

### Method 3: Manual Installation

#### For Small Deployments
1. **Download Package**
   - Obtain latest release package
   - Verify checksums for integrity

2. **Local Installation**
   - Copy installer to target machine
   - Run with administrative privileges
   - Verify installation success

3. **User Training**
   - Provide user documentation
   - Conduct training sessions if needed

### Method 4: Network Share Deployment

#### Setup
1. **Create Network Share**
   ```cmd
   # Create shared folder
   net share ArmyInventory=C:\Shared\ArmyInventory /grant:everyone,read
   ```

2. **Copy Application Files**
   - Copy portable version to network share
   - Set appropriate permissions

3. **Create Desktop Shortcuts**
   ```cmd
   # Create shortcut pointing to network location
   # Target: \\server\ArmyInventory\ArmyInventorySystem.exe
   ```

## macOS Deployment

### Method 1: Jamf Pro (Recommended)

#### Package Creation
1. **Create PKG**
   - Use Composer to create package from .app bundle
   - Include post-installation scripts if needed

2. **Upload to Jamf**
   - Upload PKG to Jamf Pro
   - Configure distribution settings

3. **Create Policy**
   - Create installation policy
   - Set scope to target computers
   - Schedule deployment

### Method 2: Manual Distribution

#### DMG Distribution
1. **Distribute DMG**
   - Email DMG file to users
   - Or place on shared network location

2. **Installation Instructions**
   - Provide clear installation guide
   - Include security bypass instructions

## Configuration Management

### Centralized Configuration

#### Windows Registry Settings
```reg
[HKEY_LOCAL_MACHINE\SOFTWARE\Army Equipment Management\Army Equipment Inventory System]
"InstallPath"="C:\\Program Files\\Army Equipment Inventory System"
"DatabasePath"="%APPDATA%\\Army Equipment Inventory System"
"AutoBackup"=dword:00000001
```

#### macOS Preferences
```bash
# Set application preferences
defaults write com.army.armyinventorysystem DatabasePath "~/Documents/ArmyInventory"
defaults write com.army.armyinventorysystem AutoBackup -bool true
```

### Database Management

#### Centralized Database (Optional)
1. **Network Database Setup**
   - Configure shared database location
   - Set appropriate permissions
   - Implement backup strategy

2. **Client Configuration**
   - Configure clients to use network database
   - Test connectivity and performance
   - Monitor for conflicts

#### Individual Databases (Recommended)
1. **Local Storage**
   - Each user maintains local database
   - Implement regular backup procedures
   - Provide data export/import tools

## Monitoring and Maintenance

### Deployment Monitoring

#### Windows
```powershell
# Check installation status
Get-WmiObject -Class Win32_Product | Where-Object {$_.Name -like "*Army Equipment Inventory*"}

# Check running processes
Get-Process -Name "ArmyInventorySystem" -ErrorAction SilentlyContinue
```

#### macOS
```bash
# Check installation
ls -la /Applications/ArmyInventorySystem.app

# Check running processes
ps aux | grep ArmyInventorySystem
```

### Update Management

#### Version Control
1. **Track Deployments**
   - Maintain inventory of deployed versions
   - Monitor for outdated installations
   - Plan update cycles

2. **Update Process**
   - Test new versions in lab environment
   - Create update packages
   - Deploy through same channels as initial installation

### User Support

#### Help Desk Preparation
1. **Training Materials**
   - Provide troubleshooting guides to help desk
   - Create knowledge base articles
   - Establish escalation procedures

2. **Common Issues**
   - Antivirus false positives
   - Permission errors
   - Database corruption
   - Performance issues

## Security Considerations

### Application Security
- [ ] Verify digital signatures
- [ ] Scan for malware before deployment
- [ ] Review application permissions
- [ ] Monitor for security updates

### Data Security
- [ ] Implement database encryption if required
- [ ] Configure backup encryption
- [ ] Review data retention policies
- [ ] Establish access controls

### Network Security
- [ ] Application doesn't require internet access
- [ ] No inbound network connections needed
- [ ] Monitor for unusual network activity
- [ ] Implement network segmentation if required

## Rollback Procedures

### Windows Rollback
```cmd
# Uninstall via Control Panel
wmic product where name="Army Equipment Inventory System" call uninstall

# Or use original installer
ArmyInventorySystem_Setup.exe /uninstall

# Clean registry if needed
reg delete "HKLM\SOFTWARE\Army Equipment Management" /f
```

### macOS Rollback
```bash
# Remove application
rm -rf /Applications/ArmyInventorySystem.app

# Remove user data (optional)
rm -rf ~/Library/Application\ Support/Army\ Equipment\ Inventory\ System
```

## Testing and Validation

### Pre-Deployment Testing
1. **Lab Environment**
   - Test on representative systems
   - Verify all features work correctly
   - Test with corporate antivirus
   - Validate performance

2. **Pilot Deployment**
   - Deploy to small group of users
   - Gather feedback
   - Monitor for issues
   - Refine deployment process

### Post-Deployment Validation
1. **Installation Verification**
   - Confirm application installed correctly
   - Test basic functionality
   - Verify database creation
   - Check for error messages

2. **User Acceptance**
   - Conduct user training
   - Gather feedback
   - Address issues promptly
   - Document lessons learned

## Support and Escalation

### Internal Support
- **Level 1**: Basic installation and usage issues
- **Level 2**: Advanced troubleshooting and configuration
- **Level 3**: Application bugs and development issues

### External Support
- **Vendor Contact**: [Vendor Support Information]
- **Emergency Contact**: [Emergency Support Contact]
- **Documentation**: [Online Documentation URL]

### Issue Tracking
- Use existing ticketing system
- Categorize issues by type and severity
- Track resolution times
- Maintain knowledge base

## Compliance and Documentation

### Change Management
- [ ] Document all changes
- [ ] Follow change approval process
- [ ] Maintain deployment records
- [ ] Update configuration management database

### Audit Requirements
- [ ] Maintain installation logs
- [ ] Document security configurations
- [ ] Track user access
- [ ] Prepare compliance reports

This deployment guide should be customized based on your organization's specific requirements, policies, and infrastructure.
