# Army Equipment Inventory System - Packaging and Distribution

## Overview

This document describes the cross-platform packaging system for the Army Equipment Inventory System. The packaging system creates standalone executables and installers for Windows and macOS without requiring Python installation on target systems.

## Quick Start

### Prerequisites
- Python 3.9 or higher
- Git (for version control)
- Platform-specific tools (see below)

### Build for Current Platform
```bash
# Simple build
python build.py

# Clean build with verbose output
python build.py --clean --verbose

# Build without installer
python build.py --no-installer
```

## Platform-Specific Requirements

### Windows
- **Required**: Python 3.9+, PyInstaller
- **Optional**: NSIS (for installer creation)
- **Recommended**: Visual Studio Build Tools

#### Installing NSIS (for Windows installers)
1. Download from https://nsis.sourceforge.io/
2. Install with default settings
3. Add to PATH: `C:\Program Files (x86)\NSIS`

### macOS
- **Required**: Python 3.9+, PyInstaller, Xcode Command Line Tools
- **Optional**: create-dmg (for better DMG creation)
- **Recommended**: iconutil (for icon conversion)

#### Installing Optional Tools
```bash
# Install create-dmg via Homebrew
brew install create-dmg

# Install icon conversion tools
brew install librsvg
```

### Linux (Experimental)
- **Required**: Python 3.9+, PyInstaller
- **Optional**: AppImage tools
- **Note**: Linux support is experimental

## Build Scripts

### Main Build Scripts
- `build.py` - Master build script (auto-detects platform)
- `build_cross_platform.py` - Python-based cross-platform builder
- `build_windows.bat` - Windows-specific batch script
- `build_macos.sh` - macOS-specific shell script

### Configuration Files
- `ArmyInventory.spec` - PyInstaller specification
- `installer_windows.nsi` - NSIS installer script
- `version_info.py` - Windows version information
- `requirements.txt` - Python dependencies

## Build Process

### 1. Dependency Check
- Verifies Python version (3.9+)
- Installs PyInstaller if missing
- Installs all requirements from requirements.txt

### 2. Resource Preparation
- Creates version file for Windows
- Checks for icon files
- Prepares resource directories

### 3. Executable Creation
- Uses PyInstaller with platform-specific configurations
- Bundles all dependencies
- Creates single executable (Windows) or app bundle (macOS)

### 4. Testing
- Performs basic executable validation
- Checks if application launches
- Verifies core functionality

### 5. Installer Creation
- **Windows**: Creates NSIS installer (.exe)
- **macOS**: Creates DMG disk image
- **Linux**: Creates AppImage (experimental)

### 6. Release Package
- Copies executables to release directory
- Includes documentation
- Creates platform-specific README files
- Generates checksums for verification

## Output Structure

```
release/
├── ArmyInventorySystem.exe              # Windows executable
├── ArmyInventorySystem.app/             # macOS app bundle
├── ArmyInventorySystem_Setup.exe        # Windows installer
├── ArmyInventorySystem_2.0.0_macOS.dmg  # macOS installer
├── run_ArmyInventorySystem.bat          # Windows run script
├── README_WINDOWS.txt                   # Windows instructions
├── README_MACOS.txt                     # macOS instructions
├── README.md                            # General documentation
├── LICENSE.txt                          # License file
├── INSTALLATION_GUIDE.md                # Installation instructions
├── TROUBLESHOOTING_GUIDE.md             # Troubleshooting guide
├── ANTIVIRUS_WHITELIST_GUIDE.md         # Antivirus configuration
├── DEPLOYMENT_GUIDE.md                  # IT deployment guide
└── checksums.txt                        # File integrity checksums
```

## Customization

### Application Metadata
Edit these variables in the build scripts:
```python
APP_NAME = "ArmyInventorySystem"
APP_VERSION = "2.0.0"
APP_DESCRIPTION = "Army Equipment Inventory Management System"
APP_AUTHOR = "Army Equipment Management"
```

### Icon Files
Place icon files in the `resources/` directory:
- `app_icon.ico` - Windows icon (256x256 recommended)
- `app_icon.icns` - macOS icon bundle
- `app_icon.svg` - Source vector icon (for conversion)

### PyInstaller Configuration
Modify `ArmyInventory.spec` to:
- Add/remove hidden imports
- Include additional data files
- Exclude unnecessary modules
- Configure platform-specific options

### Installer Customization

#### Windows (NSIS)
Edit `installer_windows.nsi`:
- Change installer appearance
- Modify installation options
- Add custom installation steps
- Configure file associations

#### macOS (DMG)
Customize DMG creation in build scripts:
- Change DMG background image
- Modify window layout
- Add custom volume icon
- Configure drag-and-drop layout

## Security and Code Signing

### Windows Code Signing
```bash
# Sign executable (requires certificate)
signtool sign /f certificate.p12 /p password /t http://timestamp.digicert.com ArmyInventorySystem.exe

# Verify signature
signtool verify /pa ArmyInventorySystem.exe
```

### macOS Code Signing
```bash
# Sign app bundle (requires Apple Developer certificate)
codesign --force --deep --sign "Developer ID Application: Your Name" ArmyInventorySystem.app

# Verify signature
codesign --verify --verbose ArmyInventorySystem.app

# Notarize for Gatekeeper (requires Apple Developer account)
xcrun altool --notarize-app --primary-bundle-id com.army.armyinventorysystem --username <EMAIL> --password @keychain:AC_PASSWORD --file ArmyInventorySystem.dmg
```

## Antivirus Considerations

### Common False Positives
PyInstaller executables may trigger antivirus false positives due to:
- Self-extracting executable behavior
- Dynamic library loading
- File system operations

### Mitigation Strategies
1. **Code Signing**: Sign executables with valid certificates
2. **Whitelisting**: Provide antivirus exclusion instructions
3. **Reputation Building**: Submit to antivirus vendors for analysis
4. **Alternative Packaging**: Consider other packaging methods if needed

## Troubleshooting

### Build Failures

#### "PyInstaller not found"
```bash
pip install pyinstaller
```

#### "Module not found" during build
Add missing modules to `hiddenimports` in `ArmyInventory.spec`

#### "Permission denied" on Windows
Run build script as administrator

#### "Code signing failed" on macOS
Ensure valid Apple Developer certificate is installed

### Runtime Issues

#### "DLL not found" on Windows
- Install Visual C++ Redistributables
- Add missing DLLs to PyInstaller spec

#### "App damaged" on macOS
- Clear quarantine: `xattr -cr ArmyInventorySystem.app`
- Sign the application properly

#### Slow startup
- Optimize PyInstaller configuration
- Exclude unnecessary modules
- Use --onedir instead of --onefile for faster startup

## Continuous Integration

### GitHub Actions Example
```yaml
name: Build Release
on:
  push:
    tags: ['v*']

jobs:
  build-windows:
    runs-on: windows-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-python@v4
        with:
          python-version: '3.9'
      - run: python build.py --clean
      - uses: actions/upload-artifact@v3
        with:
          name: windows-release
          path: release/

  build-macos:
    runs-on: macos-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-python@v4
        with:
          python-version: '3.9'
      - run: python build.py --clean
      - uses: actions/upload-artifact@v3
        with:
          name: macos-release
          path: release/
```

## Distribution

### Internal Distribution
1. **Network Shares**: Place installers on shared drives
2. **Software Deployment**: Use SCCM, Jamf, or similar tools
3. **Email Distribution**: Send installers to users (with security approval)

### External Distribution
1. **Download Portal**: Host on secure download site
2. **Physical Media**: Burn to CD/DVD or copy to USB drives
3. **Cloud Storage**: Use secure cloud storage with access controls

## Support

### Build Issues
- Check build logs for specific errors
- Verify all dependencies are installed
- Test on clean virtual machines

### Distribution Issues
- Verify file integrity with checksums
- Test installers on target systems
- Provide clear installation instructions

### Runtime Issues
- Refer to TROUBLESHOOTING_GUIDE.md
- Check antivirus logs for blocks
- Verify system requirements are met

For additional support, contact the development team or refer to the comprehensive documentation provided with each release.
