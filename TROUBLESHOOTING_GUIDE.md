# Army Equipment Inventory System - Troubleshooting Guide

## Common Installation and Runtime Issues

### Windows Issues

#### Issue: "This app can't run on your PC"
**Symptoms:** Error message when trying to run the executable
**Causes:**
- 32-bit Windows system (application requires 64-bit)
- Corrupted download
- Incompatible Windows version

**Solutions:**
1. Verify system architecture:
   ```cmd
   wmic os get osarchitecture
   ```
2. Re-download the application
3. Update Windows to latest version
4. Run compatibility troubleshooter

#### Issue: Antivirus Blocking Application
**Symptoms:** 
- Application deleted after download
- "Threat detected" notifications
- Application won't start

**Solutions:**
1. Add application to antivirus exclusions (see ANTIVIRUS_WHITELIST_GUIDE.md)
2. Temporarily disable real-time protection during installation
3. Download from official source only
4. Verify file hash if provided

#### Issue: "MSVCP140.dll is missing"
**Symptoms:** DLL error on application startup
**Cause:** Missing Microsoft Visual C++ Redistributables

**Solution:**
1. Download and install Microsoft Visual C++ Redistributable for Visual Studio 2015-2022
2. Restart computer
3. Try running application again

#### Issue: Database Access Denied
**Symptoms:** 
- "Cannot create database" error
- "Permission denied" when saving data

**Solutions:**
1. Run application as administrator (one time)
2. Check folder permissions for:
   - `%APPDATA%\Army Equipment Inventory System`
3. Ensure antivirus isn't blocking database operations
4. Move database to user-writable location

#### Issue: Excel Import Fails
**Symptoms:**
- "Cannot read Excel file" error
- Import process hangs

**Solutions:**
1. Verify Excel file format (.xlsx or .xls)
2. Close Excel file before importing
3. Check file permissions
4. Try copying file to local drive
5. Ensure file isn't corrupted

### macOS Issues

#### Issue: "App is damaged and can't be opened"
**Symptoms:** Security warning preventing application launch
**Cause:** Gatekeeper quarantine or corrupted download

**Solutions:**
1. Clear quarantine attribute:
   ```bash
   xattr -cr /Applications/ArmyInventorySystem.app
   ```
2. Re-download application
3. Verify download integrity

#### Issue: "Cannot verify developer" Warning
**Symptoms:** Security dialog blocking application
**Cause:** Application not signed with Apple Developer certificate

**Solutions:**
1. Right-click application → Open
2. Click "Open" in security dialog
3. Or go to System Preferences → Security & Privacy → "Open Anyway"

#### Issue: Application Crashes on Launch
**Symptoms:** Application starts then immediately closes
**Causes:**
- Missing system libraries
- Incompatible macOS version
- Corrupted application bundle

**Solutions:**
1. Check Console app for crash logs
2. Update macOS to latest version
3. Launch from Terminal to see error messages:
   ```bash
   /Applications/ArmyInventorySystem.app/Contents/MacOS/ArmyInventorySystem
   ```
4. Reinstall application

#### Issue: Database Permission Errors
**Symptoms:** Cannot save data or create database
**Cause:** Insufficient permissions in user directory

**Solutions:**
1. Check permissions for:
   ```bash
   ~/Library/Application Support/Army Equipment Inventory System
   ```
2. Create directory manually if needed:
   ```bash
   mkdir -p ~/Library/Application\ Support/Army\ Equipment\ Inventory\ System
   ```
3. Reset permissions:
   ```bash
   chmod 755 ~/Library/Application\ Support/Army\ Equipment\ Inventory\ System
   ```

### Cross-Platform Issues

#### Issue: Application Runs Slowly
**Symptoms:** Long startup times, sluggish interface
**Causes:**
- Insufficient RAM
- Antivirus real-time scanning
- Large database file
- Running from network drive

**Solutions:**
1. Close unnecessary applications
2. Add application to antivirus exclusions
3. Move database to local SSD
4. Increase virtual memory/swap space
5. Install on local drive instead of network

#### Issue: Excel Files Won't Open
**Symptoms:** Double-clicking Excel files doesn't open application
**Cause:** File associations not configured

**Solutions:**
1. **Windows:** Reinstall with file associations enabled
2. **macOS:** Right-click Excel file → Get Info → Open with → Select application
3. Manually open files through application File menu

#### Issue: Data Loss or Corruption
**Symptoms:** Missing records, database errors
**Causes:**
- Unexpected shutdown
- Disk space issues
- Hardware problems

**Solutions:**
1. Check for backup files in application data directory
2. Run database integrity check (if available in application)
3. Restore from backup
4. Check disk for errors:
   - **Windows:** `chkdsk C: /f`
   - **macOS:** `diskutil verifyVolume /`

## Performance Optimization

### System Requirements Check
```bash
# Windows - Check system specs
systeminfo | findstr /C:"Total Physical Memory"
systeminfo | findstr /C:"Available Physical Memory"

# macOS - Check system specs
system_profiler SPHardwareDataType | grep "Memory:"
```

### Database Optimization
1. **Regular Maintenance:**
   - Compact database monthly
   - Remove old backup files
   - Archive historical data

2. **Performance Settings:**
   - Increase application memory allocation
   - Use SSD for database storage
   - Close other applications during heavy operations

### Network Considerations
1. **Local Installation Preferred:**
   - Install application locally, not on network drives
   - Store database locally for better performance
   - Use network drives only for backups

2. **Firewall Configuration:**
   - Application doesn't require internet access
   - No inbound ports needed
   - May need file sharing ports for network database access

## Diagnostic Information Collection

### Windows Diagnostic Commands
```cmd
# System information
systeminfo > system_info.txt

# Running processes
tasklist > running_processes.txt

# Event logs (run as administrator)
wevtutil qe Application /f:text > application_events.txt

# Disk space
dir C:\ > disk_space.txt
```

### macOS Diagnostic Commands
```bash
# System information
system_profiler > system_info.txt

# Running processes
ps aux > running_processes.txt

# Console logs
log show --predicate 'process == "ArmyInventorySystem"' --last 1h > app_logs.txt

# Disk space
df -h > disk_space.txt
```

### Application-Specific Diagnostics
1. **Log Files Location:**
   - **Windows:** `%APPDATA%\Army Equipment Inventory System\logs\`
   - **macOS:** `~/Library/Application Support/Army Equipment Inventory System/logs/`

2. **Database Location:**
   - **Windows:** `%APPDATA%\Army Equipment Inventory System\inventory.db`
   - **macOS:** `~/Library/Application Support/Army Equipment Inventory System/inventory.db`

## Getting Help

### Before Contacting Support
1. **Collect Information:**
   - Operating system version
   - Application version
   - Error messages (screenshots)
   - Steps to reproduce issue
   - Log files

2. **Try Basic Solutions:**
   - Restart application
   - Restart computer
   - Check antivirus logs
   - Verify file permissions

### Contact Information
- **Technical Support:** [<EMAIL>]
- **IT Help Desk:** [<EMAIL>]
- **Emergency Support:** [emergency-contact]

### Support Ticket Information
Include the following in support requests:
1. **System Information:**
   - OS version and architecture
   - Available RAM and disk space
   - Antivirus software in use

2. **Problem Description:**
   - When the issue occurs
   - Error messages (exact text)
   - Steps that lead to the problem
   - Whether it worked before

3. **Attempted Solutions:**
   - What troubleshooting steps you've tried
   - Any changes made to the system recently
   - Whether the issue affects all users or just one

## Preventive Measures

### Regular Maintenance
1. **Weekly:**
   - Check for application updates
   - Verify database backups
   - Monitor disk space

2. **Monthly:**
   - Review antivirus logs
   - Clean temporary files
   - Update operating system

3. **Quarterly:**
   - Full system backup
   - Performance review
   - User training refresh

### Best Practices
1. **Installation:**
   - Always download from official sources
   - Verify file integrity when possible
   - Install with appropriate permissions

2. **Operation:**
   - Regular data backups
   - Don't run multiple instances
   - Close properly (don't force quit)

3. **Maintenance:**
   - Keep OS updated
   - Monitor system resources
   - Regular antivirus scans
