"""Configuration for the equipment inventory management application."""
import os
import logging
from datetime import datetime
from pathlib import Path

# Application information
APP_NAME = "InventoryTracker"
APP_VERSION = "1.0.0"
def get_app_icon():
    """Get application icon, with fallback options."""
    # Check for optimized PNG first
    png_icon = Path("resources/app_icon.png")
    if png_icon.exists() and png_icon.stat().st_size < 100*1024:  # Less than 100KB
        return str(png_icon)
    
    # Fallback to SVG if PNG doesn't exist or is too large
    svg_icon = Path("resources/app_icon.svg")
    if svg_icon.exists():
        return str(svg_icon)
    
    # No icon found
    return None

APP_ICON = get_app_icon()

# Robust database and logging configuration: always use user-writable directory and fallback if needed
import getpass

def get_user_writable_dir():
    """Get user writable directory using secure Path objects."""
    local_app_data = os.getenv("LOCALAPPDATA")
    if local_app_data:
        base_dir = Path(local_app_data)
    else:
        base_dir = Path.home()
    
    user_dir = base_dir / APP_NAME
    return str(user_dir)

def get_log_path():
    """Get log file path with validation."""
    # Allow override via environment variable
    env_log_path = os.getenv("INVENTORY_LOG_PATH")
    if env_log_path:
        log_path = Path(env_log_path)
        # Validate the path is safe
        if validate_file_path(log_path, allow_create=True):
            return str(log_path)
        else:
            logger.warning(f"Invalid log path in environment variable: {env_log_path}")
    
    user_dir = Path(get_user_writable_dir())
    user_dir.mkdir(parents=True, exist_ok=True)
    return str(user_dir / "inventory_app.log")

def get_db_path():
    """Get database file path with validation."""
    # Allow override via environment variable
    env_db_path = os.getenv("INVENTORY_DB_PATH")
    if env_db_path:
        db_path = Path(env_db_path)
        # Validate the path is safe
        if validate_file_path(db_path, allow_create=True):
            return str(db_path)
        else:
            logger.warning(f"Invalid database path in environment variable: {env_db_path}")
    
    user_dir = Path(get_user_writable_dir())
    user_dir.mkdir(parents=True, exist_ok=True)
    return str(user_dir / "inventory.db")

def validate_file_path(file_path, allow_create=False):
    """
    Validate file path for security and accessibility.
    
    Args:
        file_path (Path): Path object to validate
        allow_create (bool): Whether to allow non-existent files
        
    Returns:
        bool: True if path is valid and safe
    """
    try:
        # Convert to absolute path and resolve
        abs_path = file_path.resolve()
        
        # Check if path tries to escape user directory
        user_base = Path(get_user_writable_dir()).resolve()
        temp_base = Path.home() / "AppData" / "Local" / "Temp"
        
        # Allow paths within user directory or temp
        if not (str(abs_path).startswith(str(user_base)) or 
                str(abs_path).startswith(str(temp_base))):
            return False
        
        # Check parent directory exists or can be created
        parent = abs_path.parent
        if not parent.exists():
            if allow_create:
                try:
                    parent.mkdir(parents=True, exist_ok=True)
                except OSError:
                    return False
            else:
                return False
        
        # Check if file exists (if it should)
        if not allow_create and not abs_path.exists():
            return False
            
        return True
        
    except (OSError, ValueError):
        return False

# Environment-configurable paths
LOG_PATH = get_log_path()
DB_PATH = get_db_path()

# Environment-configurable settings
DEBUG_MODE = os.getenv("INVENTORY_DEBUG", "false").lower() == "true"
MAX_LOG_SIZE_MB = int(os.getenv("INVENTORY_MAX_LOG_SIZE_MB", "5"))
LOG_BACKUP_COUNT = int(os.getenv("INVENTORY_LOG_BACKUP_COUNT", "3"))

LOG_LEVEL = logging.INFO  # Changed from DEBUG to reduce log file size
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

# Logging initialization moved to bottom of file to avoid duplication

# Date format for display
DATE_FORMAT = '%Y-%m-%d'

# Default top-up percent for fluids (can be overridden per-fluid)
DEFAULT_TOP_UP_PERCENT = 10

# Current fiscal year (format: YYYY-YY)
current_year = datetime.now().year
next_year = current_year + 1
CURRENT_FISCAL_YEAR = f"{current_year}-{str(next_year)[-2:]}"

# UI style
UI_STYLE = """
    QMainWindow {
        background-color: #f5f5f5;
    }
    QTabWidget::pane {
        border: 1px solid #cccccc;
        background-color: white;
        border-radius: 5px;
    }
    QTabBar::tab {
        background-color: #e0e0e0;
        color: #333333;
        padding: 8px 16px;
        border: 1px solid #cccccc;
        border-bottom: none;
        border-top-left-radius: 4px;
        border-top-right-radius: 4px;
        margin-right: 2px;
    }
    QTabBar::tab:selected {
        background-color: white;
        border-bottom-color: white;
    }
    QTabBar::tab:hover:!selected {
        background-color: #eeeeee;
    }
    QGroupBox {
        font-weight: bold;
        border: 1px solid #cccccc;
        border-radius: 5px;
        margin-top: 1.5ex;
        padding-top: 1.5ex;
    }
    QGroupBox::title {
        subcontrol-origin: margin;
        subcontrol-position: top center;
        padding: 0 5px;
    }
    QPushButton {
        background-color: #0078d7;
        color: white;
        border: none;
        border-radius: 4px;
        padding: 6px 12px;
        font-weight: bold;
    }
    QPushButton:hover {
        background-color: #005a9e;
    }
    QPushButton:disabled {
        background-color: #cccccc;
        color: #888888;
    }
    QLineEdit, QSpinBox, QDoubleSpinBox, QDateEdit, QComboBox, QTextEdit {
        border: 1px solid #cccccc;
        border-radius: 4px;
        padding: 4px;
        background-color: white;
    }
    QLineEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, QDateEdit:focus, QComboBox:focus, QTextEdit:focus {
        border: 1px solid #0078d7;
    }
    QLineEdit:read-only {
        background-color: #f0f0f0;
    }
    QScrollArea {
        border: none;
    }
"""

# Initialize logging
def init_logging():
    """Initialize application logging with rotation."""
    from logging.handlers import RotatingFileHandler
    
    # Create rotating file handler (configurable size and backup count)
    file_handler = RotatingFileHandler(
        LOG_PATH, 
        maxBytes=MAX_LOG_SIZE_MB*1024*1024,  # Configurable MB
        backupCount=LOG_BACKUP_COUNT
    )
    file_handler.setLevel(LOG_LEVEL)
    file_handler.setFormatter(logging.Formatter(LOG_FORMAT))
    
    # Create console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.WARNING)  # Only show warnings/errors on console
    console_handler.setFormatter(logging.Formatter(LOG_FORMAT))
    
    # Configure root logger
    logging.basicConfig(
        level=LOG_LEVEL,
        handlers=[file_handler, console_handler]
    )
    
    # Set more restrictive log levels for some loggers
    logging.getLogger('matplotlib').setLevel(logging.WARNING)
    logging.getLogger('PIL').setLevel(logging.WARNING)

# Call init_logging when importing this module
init_logging()

# BA Number Configuration
import re

BA_NUMBER_VALIDATION_REGEX = r'^[0-9]{2}[A-Z]{1}[0-9A-Z]+$'  # Matches patterns like 86R3124A, 95R5843H, 09E020651H
REQUIRE_BA_NUMBER = False  # Make BA number optional during transition
BA_NUMBER_DISPLAY_FORMAT = "BA: {ba_number}"

def validate_ba_number(ba_number):
    """
    Validate BA number format and content.
    
    Args:
        ba_number (str): The BA number to validate
        
    Returns:
        tuple: (is_valid, error_message)
    """
    if not ba_number and not REQUIRE_BA_NUMBER:
        return True, None
    
    if not ba_number and REQUIRE_BA_NUMBER:
        return False, "BA number is required"
    
    ba_number = ba_number.strip().upper()
    
    # Check length (minimum 4 characters)
    if len(ba_number) < 4:
        return False, "BA number must be at least 4 characters long"
    
    # Check maximum length (reasonable limit)
    if len(ba_number) > 15:
        return False, "BA number cannot exceed 15 characters"
    
    # Check format using regex
    if not re.match(BA_NUMBER_VALIDATION_REGEX, ba_number):
        return False, "BA number format is invalid. Expected format: ##X#####... (e.g., 86R3124A)"
    
    return True, None

def sanitize_ba_number(ba_number):
    """
    Sanitize BA number input by removing invalid characters and normalizing format.
    
    Args:
        ba_number (str): Raw BA number input
        
    Returns:
        str: Sanitized BA number
    """
    if not ba_number:
        return ""
    
    # Remove whitespace and convert to uppercase
    ba_number = ba_number.strip().upper()
    
    # Remove non-alphanumeric characters
    ba_number = re.sub(r'[^0-9A-Z]', '', ba_number)
    
    return ba_number

# Maintenance configuration
MAINTENANCE_CRITICAL_DAYS = 7   # Under 7 days = Critical
MAINTENANCE_WARNING_DAYS = 30   # Under 30 days = Warning

# Maintenance categories and their periods
MAINTENANCE_CATEGORIES = {
    'TM-1': {'months': 6, 'display': 'TM-1 (Half Yearly)'},
    'TM-2': {'months': 12, 'display': 'TM-2 (Yearly)'},
    'Yearly': {'months': 12, 'display': 'Yearly'},
    'Monthly': {'months': 1, 'display': 'Monthly'}
}

# Maintenance types for each category
MAINTENANCE_TYPES = {
    'TM-1': ['TM-I', 'Half Yearly Service', 'Semi-Annual Check'],
    'TM-2': ['TM-II', 'Annual Service', 'Yearly Overhaul'],
    'Yearly': ['Annual Check', 'Yearly Inspection', 'Annual Maintenance'],
    'Monthly': ['Monthly Service', 'Monthly Check', 'Monthly Inspection']
}

# Updated Discard Policy Rules based on your official table
DISCARD_POLICY_RULES = {
    # Key: equipment type pattern (case-insensitive)
    # Value: {'discard_years': int, 'discard_kms': int, 'first_oh_years': int, 'first_oh_kms': int, 'hours': int, 'special_conditions': str}

    # From your official discard policy table
    'motor cycle': {
        'discard_years': 15,
        'discard_kms': 75000,
        'first_oh_years': 15,
        'first_oh_kms': 60000,
        'hours': None,
        'special_conditions': 'FIRST-OH: 15YRS AFTER INTRO OR 60,000 KMS (WHICHEVER IS EARLIER)'
    },
    'motorcycle': {
        'discard_years': 15,
        'discard_kms': 75000,
        'first_oh_years': 15,
        'first_oh_kms': 60000,
        'hours': None,
        'special_conditions': 'FIRST-OH: 15YRS AFTER INTRO OR 60,000 KMS (WHICHEVER IS EARLIER)'
    },
    'tata safari': {
        'discard_years': 9,
        'discard_kms': 100000,
        'first_oh_years': 9,
        'first_oh_kms': 60000,
        'hours': None,
        'special_conditions': None
    },
    'mahindra scorpio': {
        'discard_years': 15,
        'discard_kms': 100000,
        'first_oh_years': 9,
        'first_oh_kms': 60000,
        'hours': None,
        'special_conditions': None
    },
    'scorpio': {
        'discard_years': 15,
        'discard_kms': 100000,
        'first_oh_years': 9,
        'first_oh_kms': 60000,
        'hours': None,
        'special_conditions': None
    },
    'maruti gypsy': {
        'discard_years': 15,
        'discard_kms': 110000,
        'first_oh_years': 9,
        'first_oh_kms': 60000,
        'hours': None,
        'special_conditions': None
    },
    'gypsy': {
        'discard_years': 15,
        'discard_kms': 110000,
        'first_oh_years': 9,
        'first_oh_kms': 60000,
        'hours': None,
        'special_conditions': None
    },
    'alsamb': {
        'discard_years': 12,
        'discard_kms': 60000,
        'first_oh_years': None,
        'first_oh_kms': None,
        'hours': None,
        'special_conditions': None
    },
    'als gs veh': {
        'discard_years': 13,
        'discard_kms': 60000,
        'first_oh_years': None,
        'first_oh_kms': None,
        'hours': None,
        'special_conditions': None
    },
    'tata 25 ton': {
        'discard_years': None,
        'discard_kms': None,
        'first_oh_years': None,
        'first_oh_kms': None,
        'hours': None,
        'special_conditions': None
    },
    'tata 2.5 ton': {
        'discard_years': None,
        'discard_kms': None,
        'first_oh_years': None,
        'first_oh_kms': None,
        'hours': None,
        'special_conditions': None
    },
    'army bus': {
        'discard_years': None,
        'discard_kms': None,
        'first_oh_years': None,
        'first_oh_kms': None,
        'hours': None,
        'special_conditions': None
    },
    'als lrv': {
        'discard_years': None,
        'discard_kms': None,
        'first_oh_years': None,
        'first_oh_kms': None,
        'hours': None,
        'special_conditions': None
    },
    'als 5 kl wb': {
        'discard_years': None,
        'discard_kms': None,
        'first_oh_years': None,
        'first_oh_kms': None,
        'hours': None,
        'special_conditions': None
    },
    'dozer d-80': {
        'discard_years': None,
        'discard_kms': None,
        'first_oh_years': None,
        'first_oh_kms': None,
        'hours': 1000,
        'special_conditions': '08 YRS TO 10 YRS INTRO OR 1,000 HRS (WHICHEVER IS EARLIER)'
    },
    'dozer d 80': {
        'discard_years': None,
        'discard_kms': None,
        'first_oh_years': None,
        'first_oh_kms': None,
        'hours': 1000,
        'special_conditions': '08 YRS TO 10 YRS INTRO OR 1,000 HRS (WHICHEVER IS EARLIER)'
    },
    'jcb': {
        'discard_years': None,
        'discard_kms': None,
        'first_oh_years': None,
        'first_oh_kms': None,
        'hours': 1000,
        'special_conditions': '08 YRS TO 10 YRS INTRO OR 1,000 HRS (WHICHEVER IS EARLIER)'
    },
    'ssl': {
        'discard_years': None,
        'discard_kms': None,
        'first_oh_years': None,
        'first_oh_kms': None,
        'hours': 1000,
        'special_conditions': '08 YRS TO 10 YRS INTRO OR 1,000 HRS (WHICHEVER IS EARLIER)'
    },
    'generator set upto 5kva': {
        'discard_years': None,
        'discard_kms': None,
        'first_oh_years': None,
        'first_oh_kms': None,
        'hours': 7000,
        'special_conditions': 'AFTER 7000 HRS/09 YRS'
    },
    'generator 5kva to 30 kva': {
        'discard_years': None,
        'discard_kms': None,
        'first_oh_years': None,
        'first_oh_kms': None,
        'hours': 7000,
        'special_conditions': 'AFTER 7000 HRS/12 YRS'
    },
    'generator 30 kva above': {
        'discard_years': None,
        'discard_kms': None,
        'first_oh_years': None,
        'first_oh_kms': None,
        'hours': 7000,
        'special_conditions': 'AFTER 7000 HRS/15 YRS'
    },
    'generator': {
        'discard_years': None,
        'discard_kms': None,
        'first_oh_years': None,
        'first_oh_kms': None,
        'hours': 7000,
        'special_conditions': 'AFTER 7000 HRS (VARIES BY CAPACITY)'
    },
    'bmp-iii': {
        'discard_years': 24,
        'discard_kms': 2400,
        'first_oh_years': 16,
        'first_oh_kms': 3700,
        'hours': None,
        'special_conditions': 'FIRST-MR: 10 YRS/2400 KM, SECOND-MR: 23 YRS/5400, THIRD-MR: 35 YRS/7900 KM'
    },
    'bmp': {
        'discard_years': 24,
        'discard_kms': 2400,
        'first_oh_years': 16,
        'first_oh_kms': 3700,
        'hours': None,
        'special_conditions': 'FIRST-MR: 10 YRS/2400 KM, SECOND-MR: 23 YRS/5400, THIRD-MR: 35 YRS/7900 KM'
    },
    'aerv': {
        'discard_years': 24,
        'discard_kms': 2400,
        'first_oh_years': 16,
        'first_oh_kms': 3700,
        'hours': None,
        'special_conditions': 'FIRST-MR: 10 YRS/2400 KM, SECOND-MR: 23 YRS/5400, THIRD-MR: 35 YRS/7900 KM'
    }
}

# Legacy rules for backward compatibility
DISCARD_CRITERIA_RULES = DISCARD_POLICY_RULES

# Priority order for pattern matching (more specific patterns first)
DISCARD_CRITERIA_PRIORITY = [
    'mahindra scorpio', 'maruti gypsy', 'tata sumo', 'dozer d 80', 'bmp-ii',
    'als kal wb', 'als kal mwb', '2½ ton 4x2', '2½ ton 4x4', '2.5 ton',
    'motor cycle', 'motorcycle', 'hero', 'scorpio', 'gypsy', 'ambassador', 
    'sumo', 'als', '5 ton', '7.5 ton', 'dozer', 'dsg', 'generator', 
    'gen set', 'genset', 'bmp', 'aerv', 'truck', 'jeep', 'car', 'vehicle',
    'loader', 'excavator', 'crane', 'tractor', 'grader', 'roller'
]