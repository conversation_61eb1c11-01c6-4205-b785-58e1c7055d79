#!/usr/bin/env python3
"""
Focused Excel Column Debug Tool
This script helps identify why DATE OF REL and KM/HRS columns aren't being detected.
"""

import pandas as pd
import re
import sys
import os

def debug_excel_import(file_path):
    """Debug the exact same import process used by the application."""
    
    if not os.path.exists(file_path):
        print(f"❌ Error: File '{file_path}' not found!")
        return
    
    print(f"\n🔍 DEBUGGING EXCEL IMPORT FOR: {file_path}")
    print("=" * 80)
    
    try:
        xls = pd.ExcelFile(file_path)
        print(f"📊 Found {len(xls.sheet_names)} sheet(s): {xls.sheet_names}")
        
        for sheet_idx, sheet_name in enumerate(xls.sheet_names):
            print(f"\n📋 ANALYZING SHEET {sheet_idx + 1}: '{sheet_name}'")
            print("-" * 60)
            
            # Replicate the exact same process as the main importer
            temp = pd.read_excel(xls, sheet_name=sheet_name, header=[0,1,2])
            temp.columns = [' '.join([str(x) for x in col if str(x) != 'nan']).strip() for col in temp.columns.values]
            
            print(f"📝 Raw columns after header processing ({len(temp.columns)}):")
            for i, col in enumerate(temp.columns):
                print(f"  {i+1:2d}. '{col}'")
            
            # Check for attribute row (like the main importer does)
            attr_row = None
            remove_idx = None
            if not temp.empty:
                for idx in range(min(2, len(temp))):
                    row0 = temp.iloc[idx]
                    if row0.astype(str).str.contains(r"CAPACITY|GRADE|PERIODICITY", case=False, regex=True).any():
                        attr_row = row0
                        remove_idx = idx
                        print(f"📌 Found attribute row at index {idx}")
                        break
                if attr_row is not None:
                    temp = temp.iloc[remove_idx+1:].reset_index(drop=True)
                    print(f"📌 Removed attribute row, new dataframe shape: {temp.shape}")
            
            df = temp
            
            # Apply column normalization (exactly like main importer)
            def norm_col(col):
                return re.sub(r'[^a-z0-9]', '', str(col).replace('\n', ' ').replace('\r', ' ').replace('\t', ' ').lower())
            
            normed_cols = {norm_col(c): c for c in df.columns}
            df.columns = [norm_col(c) for c in df.columns]
            
            print(f"\n🔤 NORMALIZED COLUMNS ({len(df.columns)}):")
            for i, (norm_col, orig_col) in enumerate(normed_cols.items()):
                print(f"  {i+1:2d}. '{norm_col}' <- '{orig_col}'")
            
            # Test the exact column mapping logic from main importer
            def find_col(cols, *keywords):
                for c in cols:
                    c_low = c.lower()
                    if all(k in c_low for k in keywords):
                        return c
                return None
            
            print(f"\n🎯 TESTING COLUMN MAPPING (exact same logic as main importer):")
            
            # Test date_of_rel mapping (exact same patterns)
            date_patterns = [
                ('date', 'rel'),
                ('dt', 'rel'), 
                ('date', 'of', 'rel'),
                ('dt', 'of', 'rel'),
                ('dateofrel',),
                ('dtofrel',),
                ('release', 'date'),
                ('commission', 'date'),
                ('date', 'commission'),
                ('dt', 'commission'),
                ('induction', 'date'),
                ('date', 'induction')
            ]
            
            print("📅 DATE OF REL COLUMN SEARCH:")
            found_date_col = None
            for pattern in date_patterns:
                result = find_col(df.columns, *pattern)
                if result:
                    found_date_col = result
                    orig_col = normed_cols[result]
                    print(f"  ✅ Pattern {pattern}: FOUND -> '{result}' (original: '{orig_col}')")
                    break
                else:
                    print(f"  ❌ Pattern {pattern}: NOT FOUND")
            
            if not found_date_col:
                print(f"\n🔍 LOOKING FOR ANY DATE-LIKE COLUMNS:")
                date_keywords = ['date', 'rel', 'commission', 'induction']
                for keyword in date_keywords:
                    matches = [col for col in df.columns if keyword in col]
                    if matches:
                        for match in matches:
                            orig_col = normed_cols[match]
                            print(f"  🔍 Keyword '{keyword}': '{match}' (original: '{orig_col}')")
            
            # Test KM column mapping
            print(f"\n🚗 KM RUN COLUMN SEARCH:")
            km_patterns = [
                ('km', 'run'),
                ('kms', 'run'),
                ('kmrun',),
                ('kmsrun',),
                ('current', 'km'),
                ('curr', 'km'),
                ('total', 'km'),
                ('km', 'reading'),
                ('kilometer', 'reading'),
                ('mileage',),
                ('periodicity', 'km', 'run'),
                ('km',),
                ('kms',),
            ]
            
            found_km_col = None
            for pattern in km_patterns:
                result = find_col(df.columns, *pattern)
                if result:
                    found_km_col = result
                    orig_col = normed_cols[result]
                    print(f"  ✅ Pattern {pattern}: FOUND -> '{result}' (original: '{orig_col}')")
                    break
                else:
                    print(f"  ❌ Pattern {pattern}: NOT FOUND")
            
            # Test HRS column mapping  
            print(f"\n⏰ HRS RUN COLUMN SEARCH:")
            hrs_patterns = [
                ('hrs', 'run'),
                ('hours', 'run'),
                ('hrsrun',),
                ('hoursrun',),
                ('current', 'hrs'),
                ('curr', 'hrs'),
                ('total', 'hrs'),
                ('hrs', 'reading'),
                ('hours', 'reading'),
                ('engine', 'hrs'),
                ('engine', 'hours'),
                ('periodicity', 'hrs'),
                ('current', 'run'),
                ('curr', 'km'),
            ]
            
            found_hrs_col = None
            for pattern in hrs_patterns:
                result = find_col(df.columns, *pattern)
                if result:
                    found_hrs_col = result
                    orig_col = normed_cols[result]
                    print(f"  ✅ Pattern {pattern}: FOUND -> '{result}' (original: '{orig_col}')")
                    break
                else:
                    print(f"  ❌ Pattern {pattern}: NOT FOUND")
            
            # Show sample data from found columns
            if not df.empty:
                print(f"\n📊 SAMPLE DATA FROM FOUND COLUMNS (First 5 rows):")
                if found_date_col:
                    orig_col = normed_cols[found_date_col]
                    sample_data = df[found_date_col].head(5).tolist()
                    print(f"  📅 DATE ({orig_col}): {sample_data}")
                
                if found_km_col:
                    orig_col = normed_cols[found_km_col]
                    sample_data = df[found_km_col].head(5).tolist()
                    print(f"  🚗 KM ({orig_col}): {sample_data}")
                
                if found_hrs_col:
                    orig_col = normed_cols[found_hrs_col]
                    sample_data = df[found_hrs_col].head(5).tolist()
                    print(f"  ⏰ HRS ({orig_col}): {sample_data}")
                
                # Show all columns with their sample data if no matches found
                if not any([found_date_col, found_km_col, found_hrs_col]):
                    print(f"\n📋 ALL COLUMNS WITH SAMPLE DATA:")
                    for col in df.columns[:10]:  # Show first 10 columns
                        orig_col = normed_cols[col]
                        sample_data = df[col].head(3).tolist()
                        print(f"  📄 '{orig_col}': {sample_data}")
            
            # Only analyze first sheet
            break
            
    except Exception as e:
        print(f"❌ Error analyzing file: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 80)
    print("🎯 RECOMMENDATIONS:")
    print("1. Check if your Excel file has the expected structure")
    print("2. Verify column names match the search patterns")
    print("3. Check if data is in the expected rows (not in headers)")
    print("4. Consider renaming columns to match expected patterns")
    print("=" * 80)

def main():
    if len(sys.argv) != 2:
        print("Usage: python debug_excel_columns.py <path_to_excel_file>")
        print("Example: python debug_excel_columns.py equipment_data.xlsx")
        return
    
    excel_file = sys.argv[1]
    debug_excel_import(excel_file)

if __name__ == "__main__":
    main() 