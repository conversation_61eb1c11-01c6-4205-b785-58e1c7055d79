"""
Excel import utility for InventoryTracker application.
"""
import pandas as pd
import logging
import re
from datetime import date
from collections import defaultdict
import numpy as np
# Import models lazily to avoid database access during module import
from dateutil import parser as date_parser

def ensure_date_object(date_value):
    """Convert any date representation to a datetime.date object."""
    if date_value is None:
        return None
    if isinstance(date_value, date):
        return date_value
    try:
        if isinstance(date_value, str):
            return date.fromisoformat(date_value.split(' ')[0])
        elif isinstance(date_value, pd.Timestamp):
            return date_value.date()
        else:
            # Handle other types that might have a date method
            return date_value.date() if hasattr(date_value, 'date') else None
    except Exception as e:
        logging.debug(f"Could not convert {date_value} to date: {e}")
        return None

logger = logging.getLogger('excel_importer')

def parse_excel_date(val):
    if pd.isna(val) or str(val).strip() in ('', '#######', '-', 'NA', 'N/A', 'nan', 'NaN'):
        return None
    
    try:
        # Handle pandas Timestamp and Python date objects
        if isinstance(val, (pd.Timestamp, date)):
            return val.strftime('%Y-%m-%d')
        
        # Handle numeric values (Excel serial numbers)
        if isinstance(val, (int, float)):
            # Excel dates are stored as days since 1900-01-01 (with 1900-01-01 = 1)
            # But Excel incorrectly treats 1900 as a leap year, so we need to adjust
            if 1 <= val <= 2958465:  # Valid Excel date range (1900-01-01 to 9999-12-31)
                try:
                    # Convert Excel serial number to date
                    excel_epoch = pd.Timestamp('1899-12-30')  # Excel's epoch (accounting for the leap year bug)
                    parsed_date = excel_epoch + pd.Timedelta(days=val)
                    return parsed_date.strftime('%Y-%m-%d')
                except:
                    pass  # Fall through to string parsing
        
        # Handle string values
        s = str(val).replace('\\', '/').replace('.', '/').replace('-', '/').strip().upper()
        
        # Skip obvious non-date strings
        if any(keyword in s for keyword in ['UNDER', 'MLOH', 'YRS', 'YEAR', 'MONTH', 'NA', 'NULL']):
            return None
        
        # Handle multiple dates in one cell (take the first one)
        if len(s.split()) > 1 and any(char in s for char in ['/', '-']):
            # Split by spaces and take the first date-like part
            parts = s.split()
            for part in parts:
                if any(char in part for char in ['/', '-']) and len(part) >= 6:
                    s = part
                    break
        
        # Handle DD/MM/YYYY format (common in your data like 23/07/2000)
        if '/' in s and len(s.split('/')) == 3:
            parts = s.split('/')
            try:
                if len(parts[0]) <= 2 and len(parts[1]) <= 2 and len(parts[2]) >= 2:
                    # Fix common typos like '23/03/20211' -> '23/03/2021'
                    if len(parts[2]) > 4:
                        parts[2] = parts[2][:4]
                    
                    # Validate day and month ranges
                    day, month, year = int(parts[0]), int(parts[1]), int(parts[2])
                    
                    # Convert 2-digit years to 4-digit (assumes 20xx for 00-30, 19xx for 31-99)
                    if year < 100:
                        year = 2000 + year if year <= 30 else 1900 + year
                    
                    # Validate date components
                    if 1 <= day <= 31 and 1 <= month <= 12 and 1900 <= year <= 2100:
                        # Check for invalid dates like 31/06 (June doesn't have 31 days)
                        try:
                            test_date = date(year, month, day)
                            return test_date.strftime('%Y-%m-%d')
                        except ValueError:
                            # Invalid date (e.g., 31/06/25)
                            return None
            except (ValueError, IndexError):
                pass
        
        # Try to parse with dateutil as fallback
        try:
            parsed_date = date_parser.parse(s, dayfirst=True)
            # Validate the parsed year is reasonable
            if 1900 <= parsed_date.year <= 2100:
                return parsed_date.strftime('%Y-%m-%d')
        except:
            pass
            
        return None
        
    except Exception as e:
        logger.debug(f"Could not parse date '{val}': {e}")
        return None

def parse_int(val):
    if pd.isna(val) or str(val).strip() in ('', '#######', '-', 'NA', 'N/A'):
        return 0
    s = str(val).replace(',', '').upper()
    
    # Handle range formats like "8 to 10 Year" - take the average
    if ' TO ' in s:
        try:
            parts = s.replace('YEAR', '').replace('YRS', '').replace('YR', '').split(' TO ')
            if len(parts) == 2:
                start = float(re.sub(r'[^0-9\.-]', '', parts[0].strip()))
                end = float(re.sub(r'[^0-9\.-]', '', parts[1].strip()))
                return int((start + end) / 2)  # Return average
        except Exception:
            pass
    
    # Handle complex formats like "5000 Hrs or 105000" - extract the larger number (likely KM)
    if ' OR ' in s:
        try:
            parts = s.split(' OR ')
            numbers = []
            for part in parts:
                # Extract numbers from each part
                num_str = re.sub(r'[^0-9\.-]', '', part.strip())
                if num_str:
                    numbers.append(float(num_str))
            if numbers:
                return int(max(numbers))  # Return the larger number
        except Exception:
            pass
    
    if 'YR' in s:
        s = s.split('YR')[0]
    if 'KM' in s:
        s = s.split('KM')[0]
    try:
        return int(float(s))
    except Exception:
        return 0

def parse_km_hrs(val):
    """Parse combined KM/HRS values like '625/267' or '1785/1756.2' into separate KM and HRS values."""
    if pd.isna(val) or str(val).strip() in ('', '#######', '-', 'NA', 'N/A'):
        return 0.0, 0.0
    
    s = str(val).replace(',', '').strip()
    
    # Handle slash-separated values (KM/HRS format)
    if '/' in s:
        try:
            parts = s.split('/')
            if len(parts) >= 2:
                km_part = parts[0].strip()
                hrs_part = parts[1].strip()
                
                # Extract numeric values
                km = float(re.sub(r'[^0-9\.-]', '', km_part)) if km_part else 0.0
                hrs = float(re.sub(r'[^0-9\.-]', '', hrs_part)) if hrs_part else 0.0
                
                return km, hrs
        except Exception:
            pass
    
    # Fallback: treat as single numeric value (assume it's KM)
    try:
        single_val = float(re.sub(r'[^0-9\.-]', '', s))
        return single_val, 0.0
    except Exception:
        return 0.0, 0.0

def parse_km_yrs(val):
    # Handles '10000 KM/10 YRS' or '10000 KM/10 YRS'
    if pd.isna(val) or str(val).strip() in ('', '#######', '-', 'NA', 'N/A'):
        return 0, 0
    s = str(val).upper().replace(' ', '')
    km, yrs = 0, 0
    if '/' in s:
        parts = s.split('/')
        for p in parts:
            if 'KM' in p:
                km = parse_int(p)
            elif 'YR' in p:
                yrs = parse_int(p)
    else:
        if 'KM' in s:
            km = parse_int(s)
        if 'YR' in s:
            yrs = parse_int(s)
    return km, yrs

def import_from_excel(file_path):
    """
    Import data from an Excel file into the database.
    Sheets should have two header rows, with data starting from row 3.
    Returns a dict with import statistics.
    """
    # Import models here to avoid database access during module import
    from models import Equipment, Fluid, DiscardCriteria, TyreMaintenance, Repair, Maintenance, Overhaul, Battery, MediumReset

    logger.info(f"Starting Excel import from {file_path}")

    xls = pd.ExcelFile(file_path)
    existing_equip = Equipment.get_all() or []
    serial_counter = len(existing_equip) + 1
    stats = {
        'equipment': 0,
        'fluids': 0,
        'tyres': 0,
        'batteries': 0,
        'repairs': 0,
        'medium_resets': 0,
        'skipped': 0,
        'overhauls': 0
    }
    
    for sheet_name in xls.sheet_names:
        # Read with 3 header rows and flatten columns
        temp = pd.read_excel(xls, sheet_name=sheet_name, header=[0,1,2])
        temp.columns = [' '.join([str(x) for x in col if str(x) != 'nan']).strip() for col in temp.columns.values]
        attr_row = None
        remove_idx = None
        if not temp.empty:
            for idx in range(min(2, len(temp))):
                row0 = temp.iloc[idx]
                if row0.astype(str).str.contains(r"CAPACITY|GRADE|PERIODICITY", case=False, regex=True).any():
                    attr_row = row0
                    remove_idx = idx
                    break
            if attr_row is not None:
                temp = temp.iloc[remove_idx+1:].reset_index(drop=True)
        df = temp
        
        # Improved normalization: remove newlines, unify spaces, lower-case
        def norm_col(col):
            return re.sub(r'[^a-z0-9]', '', str(col).replace('\n', ' ').replace('\r', ' ').replace('\t', ' ').lower())
        normed_cols = {norm_col(c): c for c in df.columns}
        df.columns = [norm_col(c) for c in df.columns]
        
        # Helper to robustly find columns by keywords
        def find_col(cols, *keywords):
            for c in cols:
                c_low = c.lower()
                if all(k in c_low for k in keywords):
                    return c
            return None
        
        # Map normalized columns to logical names using robust matching
        col_map = {}
        col_map['serial'] = find_col(df.columns, 'serial', 'no')
        col_map['make_and_type'] = find_col(df.columns, 'make', 'type')
        col_map['ba_number'] = find_col(df.columns, 'ba', 'no') or find_col(df.columns, 'ba', 'number')
        col_map['units'] = find_col(df.columns, 'unit')
        col_map['vintage'] = find_col(df.columns, 'vintage', 'years') or \
                             find_col(df.columns, 'years', 'run') or \
                             find_col(df.columns, 'periodicity', 'years', 'run') or \
                             find_col(df.columns, 'periodicity', 'year') or \
                             find_col(df.columns, 'vintage') # Keep original as last fallback
        col_map['meterage'] = find_col(df.columns, 'meterage')
        
        # Keep original prev_run and curr_run mappings for backward compatibility
        col_map['prev_run'] = find_col(df.columns, 'periodicity', 'km', 'run') or \
                              find_col(df.columns, 'current', 'km') or \
                              find_col(df.columns, 'curr', 'km') or \
                              find_col(df.columns, 'km', 'run') or \
                              find_col(df.columns, 'kms', 'run') or \
                              find_col(df.columns, 'kilometer', 'reading') or \
                              find_col(df.columns, 'km') or \
                              find_col(df.columns, 'kms') or \
                              find_col(df.columns, 'kilometers') or \
                              find_col(df.columns, 'mileage') or \
                              find_col(df.columns, 'periodicity', 'km') or \
                              find_col(df.columns, 'previous', 'run') or \
                              find_col(df.columns, 'prev', 'km')
        col_map['curr_run'] = find_col(df.columns, 'current', 'run') or \
                              find_col(df.columns, 'curr', 'km') or \
                              find_col(df.columns, 'periodicity', 'hrs')
        
        # Enhanced date of release detection with more patterns including embedded date formats
        col_map['date_of_rel'] = find_col(df.columns, 'date', 'rel') or \
                                 find_col(df.columns, 'dt', 'rel') or \
                                 find_col(df.columns, 'date', 'of', 'rel') or \
                                 find_col(df.columns, 'dt', 'of', 'rel') or \
                                 find_col(df.columns, 'dateofrel') or \
                                 find_col(df.columns, 'dtofrel') or \
                                 find_col(df.columns, 'release', 'date') or \
                                 find_col(df.columns, 'commission', 'date') or \
                                 find_col(df.columns, 'date', 'commission') or \
                                 find_col(df.columns, 'dt', 'commission') or \
                                 find_col(df.columns, 'induction', 'date') or \
                                 find_col(df.columns, 'date', 'induction') or \
                                 next((c for c in df.columns if 'dateofrel' in c and any(char.isdigit() for char in c)), None)
        
        col_map['remarks'] = find_col(df.columns, 'remark')
        col_map['km_run'] = find_col(df.columns, 'km', 'run') or \
                           find_col(df.columns, 'kms', 'run') or \
                           find_col(df.columns, 'kmrun') or \
                           find_col(df.columns, 'kmsrun') or \
                           find_col(df.columns, 'current', 'km') or \
                           find_col(df.columns, 'curr', 'km') or \
                           find_col(df.columns, 'total', 'km') or \
                           find_col(df.columns, 'km', 'reading') or \
                           find_col(df.columns, 'kilometer', 'reading') or \
                           find_col(df.columns, 'mileage') or \
                           col_map.get('prev_run')  # Use existing prev_run as fallback
        col_map['hrs_run'] = find_col(df.columns, 'hrs', 'run') or \
                            find_col(df.columns, 'hours', 'run') or \
                            find_col(df.columns, 'hrsrun') or \
                            find_col(df.columns, 'hoursrun') or \
                            find_col(df.columns, 'current', 'hrs') or \
                            find_col(df.columns, 'curr', 'hrs') or \
                            find_col(df.columns, 'total', 'hrs') or \
                            find_col(df.columns, 'hrs', 'reading') or \
                            find_col(df.columns, 'hours', 'reading') or \
                            find_col(df.columns, 'engine', 'hrs') or \
                            find_col(df.columns, 'engine', 'hours') or \
                            col_map.get('curr_run')  # Use existing curr_run as fallback
        
        # Enhanced debugging for column detection
        logger.info(f"=== EXCEL IMPORT DEBUG INFO FOR SHEET: {sheet_name} ===")
        logger.info(f"Total columns found: {len(df.columns)}")
        logger.info(f"All available columns: {list(df.columns)}")
        logger.info(f"Normalized columns mapping:")
        for norm_col, orig_col in normed_cols.items():
            if any(keyword in norm_col for keyword in ['date', 'rel', 'km', 'hrs', 'run']):
                logger.info(f"  '{norm_col}' -> '{orig_col}'")
        
        logger.info(f"Enhanced column mapping results:")
        logger.info(f"  date_of_rel: '{col_map.get('date_of_rel')}' -> '{normed_cols.get(col_map.get('date_of_rel'), 'NOT FOUND') if col_map.get('date_of_rel') else 'NOT FOUND'}'")
        logger.info(f"  km_run: '{col_map.get('km_run')}' -> '{normed_cols.get(col_map.get('km_run'), 'NOT FOUND') if col_map.get('km_run') else 'NOT FOUND'}'")
        logger.info(f"  hrs_run: '{col_map.get('hrs_run')}' -> '{normed_cols.get(col_map.get('hrs_run'), 'NOT FOUND') if col_map.get('hrs_run') else 'NOT FOUND'}'")
        logger.info(f"  prev_run: '{col_map.get('prev_run')}' -> '{normed_cols.get(col_map.get('prev_run'), 'NOT FOUND') if col_map.get('prev_run') else 'NOT FOUND'}'")
        logger.info(f"  curr_run: '{col_map.get('curr_run')}' -> '{normed_cols.get(col_map.get('curr_run'), 'NOT FOUND') if col_map.get('curr_run') else 'NOT FOUND'}'")
        logger.info(f"=== END DEBUG INFO ===")
        
        # Sample first row data for debugging
        if not df.empty:
            sample_row = df.iloc[0]
            logger.info(f"Sample first row data:")
            for key, col in col_map.items():
                if col and key in ['date_of_rel', 'km_run', 'hrs_run', 'prev_run', 'curr_run']:
                    value = sample_row.get(col, 'N/A')
                    logger.info(f"  {key} ({col}): '{value}'")
        
        # Forward-fill only 'vintage' since it represents merged header rows within the same vehicle group.
        # Do NOT forward-fill meterage or run columns globally, as each row represents a different vehicle.
        # This prevents incorrect carry-over of KM values.
        if col_map.get('vintage'):
            df[col_map['vintage']] = df[col_map['vintage']].ffill()
        
        # Note: Removed global forward-fill for 'meterage', 'prev_run', and 'curr_run'. Any required carry-over is now handled per-row within the loop when appropriate.
        
        # Keep track of last valid values as backup for edge cases
        last_vintage = 0
        last_meterage = 0
        last_prev_run = 0
        last_curr_run = 0
        
        for _, row in df.iterrows():
            # Use normalized column names for all lookups
            make_and_type = row.get(col_map.get('make_and_type', ''), '')
            if not make_and_type or pd.isna(make_and_type) or str(make_and_type).strip()=='':
                stats['skipped'] += 1
                continue
            serial = str(serial_counter)
            serial_counter += 1
            
            # Detect new equipment row and reset forward-fill caches to avoid carry-over between different vehicles
            if make_and_type and not pd.isna(make_and_type) and str(make_and_type).strip() != '':
                last_vintage = 0
                last_meterage = 0
                last_prev_run = 0
                last_curr_run = 0
            
            # Extract BA number from Excel or leave empty for manual assignment
            ba_number = row.get(col_map.get('ba_number', ''), '')
            if ba_number and not pd.isna(ba_number):
                ba_number = str(ba_number).strip()
            else:
                ba_number = None  # Will be assigned manually later
            
            units = int(row.get(col_map.get('units', ''), 1))
            
            # Enhanced vintage and meterage parsing with merged cell handling
            vintage_raw = row.get(col_map.get('vintage', ''), 0)
            meterage_raw = row.get(col_map.get('meterage', ''), 0)
            date_of_rel_raw = row.get(col_map.get('date_of_rel', ''), None)
            
            date_of_rel_parsed = parse_excel_date(date_of_rel_raw) if date_of_rel_raw else None
            
            if date_of_rel_raw and not date_of_rel_parsed:
                logger.warning(f"Failed to parse date '{date_of_rel_raw}' for equipment {make_and_type}")

            logger.debug(f"Raw values: vintage_raw='{vintage_raw}', meterage_raw='{meterage_raw}' for {make_and_type}")
            # Parse and validate vintage
            vintage_parsed = parse_int(vintage_raw)
            if vintage_parsed > 0:
                vintage = float(vintage_parsed)
                last_vintage = vintage  # Update last valid value
            elif not pd.isna(vintage_raw) and str(vintage_raw).strip() != '' and str(vintage_raw).strip() != '0':
                # Try to parse non-zero string values
                try:
                    vintage_val = float(str(vintage_raw).strip())
                    if vintage_val > 0:
                        vintage = vintage_val
                        last_vintage = vintage
                    else:
                        vintage = last_vintage
                except:
                    vintage = last_vintage
            else:
                vintage = last_vintage  # Use last valid value for merged cells
            
            # Parse and validate meterage - preserve original description text and extract numeric value
            meterage_parsed = parse_int(meterage_raw)
            meterage_description = None
            
            # Always preserve the original text description if it's meaningful
            meterage_str = str(meterage_raw).strip()
            if (not pd.isna(meterage_raw) and 
                meterage_str not in ['', '0', '-', 'nan', 'NaN', 'NA', 'N/A'] and
                meterage_str != str(meterage_parsed)):  # Only store if different from numeric value
                meterage_description = meterage_str
            
            if meterage_parsed > 0:
                meterage = float(meterage_parsed)
                last_meterage = meterage  # Update last valid value
            else:
                # Check if raw value has useful data that parse_int couldn't handle
                if (not pd.isna(meterage_raw) and 
                    meterage_str not in ['', '0', '-', 'nan', 'NaN', 'NA', 'N/A']):
                    # Try alternative parsing for complex formats
                    try:
                        # Handle formats like "5000 Hrs or 105000" by looking for numbers
                        numbers = re.findall(r'\d+', meterage_str)
                        if numbers:
                            # Take the largest number found (likely the KM value)
                            meterage_val = float(max(numbers, key=lambda x: int(x)))
                            if meterage_val > 0:
                                meterage = meterage_val
                                last_meterage = meterage
                                # Always preserve the original text for complex formats
                                meterage_description = meterage_str
                            else:
                                meterage = last_meterage
                        else:
                            meterage = last_meterage
                    except:
                        meterage = last_meterage
                else:
                    meterage = last_meterage  # Use last valid value for merged cells
            
            # If still zero after all attempts, keep as 0 (do not carry forward)
            if meterage == last_meterage == 0:
                meterage = 0
                meterage_description = None
            
            # Parse KM/HRS values - Try enhanced column mapping first, then fallback to original
            km_run_raw = row.get(col_map.get('km_run', ''), 0) or row.get(col_map.get('prev_run', ''), 0)
            hrs_run_raw = row.get(col_map.get('hrs_run', ''), 0) or row.get(col_map.get('curr_run', ''), 0)
            
            logger.debug(f"KM/HRS extraction for {make_and_type}: km_run_raw={repr(km_run_raw)}, hrs_run_raw={repr(hrs_run_raw)}")
            
            # Parse KM value directly (not as KM/HRS format)
            try:
                km_val = parse_int(km_run_raw) if km_run_raw not in ['', 'nan', '-', None] and not pd.isna(km_run_raw) else 0
            except:
                km_val = 0
            
            # Parse HRS value directly (not as KM/HRS format)  
            try:
                hrs_val = parse_int(hrs_run_raw) if hrs_run_raw not in ['', 'nan', '-', None] and not pd.isna(hrs_run_raw) else 0
            except:
                hrs_val = 0
            
            # Handle merged cells for run data
            if km_val > 0:
                last_prev_run = km_val
                prev_run = km_val
                curr_run = km_val  # For backward compatibility
            else:
                prev_run = last_prev_run
                curr_run = last_prev_run
            
            # Handle hours data
            if hrs_val > 0:
                total_hours = hrs_val
                # Assume previous month was 0 and current is the total for simplicity
                previous_month_hours = 0
                current_month_hours = hrs_val
            else:
                # No hours data available
                current_month_hours = 0
                previous_month_hours = 0
                total_hours = 0
                
            logger.debug(f"Calculated hours: total={total_hours}, prev_month={previous_month_hours}, curr_month={current_month_hours}")

            # If 'meterage' (which populates meterage_kms) is still 0 or not meaningfully parsed,
            # but we have a valid 'km_val' (from 'prev_run' alias), use 'km_val' for 'meterage'.
            # This ensures the primary KM reading from the Excel (likely in a 'Current KM' or similar column
            # mapped to 'prev_run') is used for 'meterage'.
            if (meterage == 0 or meterage == last_meterage == 0) and prev_run > 0 : # Ensure prev_run is from a valid current parsing, not just a carried-over last_prev_run if km_val was 0
                if km_val > 0 : # km_val is the most current parsing of the prev_run column
                     meterage = float(km_val)
                     logger.debug(f"Updated 'meterage' to {meterage} from 'km_val' ({km_val}) as primary 'meterage' was zero or unparsed.")
                elif prev_run > 0 and last_meterage == 0 : # If km_val was 0, but prev_run (potentially from ffill) is valid
                     meterage = float(prev_run)
                     logger.debug(f"Updated 'meterage' to {meterage} from 'prev_run' ({prev_run}) due to ffill, as primary 'meterage' was zero.")

            # Also ensure meterage_description is updated if meterage was just derived from km_val/prev_run
            # and the original km_run_raw had some descriptive text.
            if (meterage == float(km_val if km_val > 0 else prev_run)) and \
               (meterage_description is None or meterage_description == str(int(last_meterage if last_meterage > 0 else 0)) ) and \
               isinstance(km_run_raw, str) and \
               km_run_raw.strip() not in ['', '0', '-', 'nan', 'NaN', 'NA', 'N/A'] and \
               km_run_raw.strip() != str(int(meterage)): # Check against the final meterage value
                meterage_description = km_run_raw.strip()
                logger.debug(f"Updated 'meterage_description' to '{meterage_description}' from 'km_run_raw'.")
            
            remarks = row.get(col_map.get('remarks', ''), '')
            
            logger.debug(f"Final values for {make_and_type}: date_of_commission={repr(date_of_rel_parsed)}, meterage_kms={repr(meterage)}, hours_run_total={repr(total_hours)}")
            
            existing = Equipment.get_by_serial(str(serial))
            if existing:
                eq = Equipment(
                    equipment_id=existing['equipment_id'],
                    serial_number=str(serial),
                    make_and_type=str(make_and_type),
                    ba_number=ba_number,
                    units_held=units,
                    vintage_years=vintage,
                    meterage_kms=meterage,
                    meterage_description=meterage_description,
                    km_hrs_run_previous_month=prev_run,
                    km_hrs_run_current_month=curr_run,
                    hours_run_total=total_hours,  # Use the current total
                    hours_run_previous_month=previous_month_hours,
                    hours_run_current_month=current_month_hours,
                    is_active=True,
                    remarks=remarks,
                    date_of_commission=date_of_rel_parsed
                )
            else:
                eq = Equipment(
                    serial_number=str(serial),
                    make_and_type=str(make_and_type),
                    ba_number=ba_number,
                    units_held=units,
                    vintage_years=vintage,
                    meterage_kms=meterage,
                    meterage_description=meterage_description,
                    km_hrs_run_previous_month=prev_run,
                    km_hrs_run_current_month=curr_run,
                    hours_run_total=total_hours,  # Use the current total
                    hours_run_previous_month=previous_month_hours,
                    hours_run_current_month=current_month_hours,
                    is_active=True,
                    remarks=remarks,
                    date_of_commission=date_of_rel_parsed
                )
            
            eq_id = eq.save()
            if not eq_id:
                logger.warning(f"Failed to save equipment: serial='{serial}', type='{make_and_type}'")
                stats['skipped'] += 1
                continue
            stats['equipment'] += 1
            
            # Robust Fluid Import
            fluid_types = [
                'engoil', 'txnoil', 'hydraulicfluid', 'grease', 'coolant', 'gearbox', 'auxgearbox', 'differentialfront', 'differentialrear', 'steeringgearbox', 'brake', 'clutch'
            ]
            for fluid in fluid_types:
                fluid_cols = [c for c in df.columns if fluid in c]
                if not fluid_cols:
                    continue
                capacity_col = next((c for c in fluid_cols if 'capacity' in c), None)
                grade_col = next((c for c in fluid_cols if 'grade' in c), None)
                addl_col = next((c for c in fluid_cols if 'addl' in c or 'topup' in c), None)
                periodicity_col = next((c for c in fluid_cols if 'periodicity' in c), None)
                date_change_col = next((c for c in fluid_cols if ('date' in c and 'change' in c) or ('dt' in c and 'change' in c) or 'dtoflastchange' in c or 'dtofchange' in c), None)
                
                # Get values
                capacity = row.get(capacity_col) if capacity_col else None
                grade = row.get(grade_col) if grade_col else None
                addl = row.get(addl_col) if addl_col else None
                periodicity = row.get(periodicity_col) if periodicity_col else None
                date_of_change = row.get(date_change_col) if date_change_col else None
                
                # Only create if at least one value is present
                if any([capacity, grade, addl, periodicity, date_of_change]):
                    def parse_capacity(val):
                        if val is None:
                            return 0.0
                        if isinstance(val, (int, float)):
                            return float(val)
                        s = str(val).upper().replace(',', '').strip()
                        s = re.sub(r'[^0-9\.-]', '', s)
                        try:
                            return float(s)
                        except Exception:
                            return 0.0
                    
                    # Parse date of change
                    parsed_date_of_change = None
                    if date_of_change:
                        parsed_date_of_change = parse_excel_date(date_of_change)
                    
                    from models import Fluid
                    fluid_obj = Fluid(
                        equipment_id=eq_id,
                        fluid_type=fluid,
                        capacity_ltrs_kg=parse_capacity(capacity) if capacity is not None else 0.0,
                        grade=str(grade) if grade is not None else None,
                        addl_10_percent_top_up=parse_capacity(addl) if addl is not None else 0.0,
                        periodicity_km=parse_int(periodicity) if periodicity is not None else 0,
                        periodicity_hrs=0,
                        periodicity_months=0,
                        date_of_change=parsed_date_of_change
                    )
                    fluid_obj.save()
                    stats['fluids'] = stats.get('fluids', 0) + 1
            
            # Improved Maintenance/Overhaul Matching with Category Assignment
            # Group maintenance data by type to create complete records
            maintenance_data = {}
            
            # Collect all maintenance-related columns for each type
            maint_types = ['TM-I', 'TM-II', 'TM-III', 'TM-IV', 'TM-1', 'TM-2', 'Annual Check', 'Monthly Check']
            maint_keys = ['tmi', 'tmii', 'tmiii', 'tmiv', 'tm1', 'tm2', 'annual', 'monthly']
            
            # Map maintenance types to categories
            maintenance_category_map = {
                'TM-I': 'TM-1',          # TM-I goes to TM-1 tab
                'TM-II': 'TM-2',         # TM-II goes to TM-2 tab
                'TM-III': 'TM-1',        # TM-III goes to TM-1 tab (fallback)
                'TM-IV': 'TM-1',         # TM-IV goes to TM-1 tab (fallback)
                'TM-1': 'TM-1',          # Direct TM-1 mapping
                'TM-2': 'TM-2',          # Direct TM-2 mapping
                'Annual Check': 'Yearly', # Annual maintenance goes to Yearly tab
                'Monthly Check': 'Monthly' # Monthly maintenance goes to Monthly tab
            }
            
            for i, (maint_type, key) in enumerate(zip(maint_types, maint_keys)):
                # Try multiple column matching patterns for flexibility
                done_col = (find_col(df.columns, key, 'done') or 
                           find_col(df.columns, key, 'completion') or
                           find_col(df.columns, key, 'completed'))
                due_col = (find_col(df.columns, key, 'due') or
                          find_col(df.columns, key, 'next') or
                          find_col(df.columns, key, 'schedule'))
                
                done_val = row.get(done_col) if done_col else None
                due_val = row.get(due_col) if due_col else None
                
                done_date = parse_excel_date(done_val) if done_val else None
                due_date = parse_excel_date(due_val) if due_val else None
                
                # Only create maintenance record if we have at least one date
                if done_date or due_date:
                    maintenance_data[maint_type] = {
                        'done_date': done_date,
                        'due_date': due_date,
                        'category': maintenance_category_map.get(maint_type, 'TM-1')
                    }
            
            # Create complete maintenance records with proper categories
            for maint_type, data in maintenance_data.items():
                m = Maintenance(
                    equipment_id=eq_id,
                    maintenance_type=maint_type,
                    done_date=data['done_date'],
                    due_date=data['due_date'],
                    vintage_years=vintage,
                    meterage_kms=meterage,
                    maintenance_category=data['category']
                )
                m.save()
            
            # --- NEW: Overhaul import (OH-I / OH-II) ---
            try:
                # Identify overhaul columns once per sheet (outside row loop ideally), but lightweight check here
                oh1_done_col = find_col(df.columns, 'oh', '1', 'done') or find_col(df.columns, 'ohi', 'done')
                oh1_due_col = find_col(df.columns, 'oh', '1', 'due') or find_col(df.columns, 'ohi', 'due')
                oh2_done_col = find_col(df.columns, 'oh', '2', 'done') or find_col(df.columns, 'ohii', 'done')
 
                 # Extract values
                oh1_done_raw = row.get(oh1_done_col) if oh1_done_col else None
                oh1_due_raw = row.get(oh1_due_col) if oh1_due_col else None
                oh2_done_raw = row.get(oh2_done_col) if oh2_done_col else None
 
                oh1_done_date = parse_excel_date(oh1_done_raw) if oh1_done_raw else None
                oh1_due_date_excel = parse_excel_date(oh1_due_raw) if oh1_due_raw else None
                oh2_done_date = parse_excel_date(oh2_done_raw) if oh2_done_raw else None
 
                # -------------------------------------------------------------
                # FIXED: Always calculate correct due dates based on business rules
                # DO NOT trust Excel due dates as they are often incorrect
                # -------------------------------------------------------------
                import overhaul_service
                from datetime import date as _date
 
                # OH-I due date: ALWAYS calculate correctly (commission + 15 years)
                oh1_due_date = None
                if date_of_rel_parsed:
                    oh1_due_date = overhaul_service.calculate_oh1_due_date(date_of_rel_parsed)
                    
                    # Log if Excel had different due date (for debugging)
                    if oh1_due_date_excel and oh1_due_date:
                        date1 = ensure_date_object(oh1_due_date)
                        date2 = ensure_date_object(oh1_due_date_excel)
                        if date1 and date2 and abs((date1 - date2).days) > 365:  # More than 1 year difference
                            logger.warning(f"Equipment {eq_id}: Corrected OH-I due date from Excel {oh1_due_date_excel} to calculated {oh1_due_date}")
 
                # OH-II due date: only if OH-I is completed
                oh2_due_date = overhaul_service.calculate_oh2_due_date(oh1_done_date) if oh1_done_date else None
 
                from models import Overhaul
 
                 # Helper to upsert overhaul
                def upsert(overhaul_type, done_date, due_date):
                    # Ensure we at least have a due date; done_date may be None for scheduled records
                    if due_date is None:
                        return False
                    existing_list = Overhaul.get_by_equipment(eq_id) or []
                    existing = next((o for o in existing_list if o.get('overhaul_type') == overhaul_type), None)
                    status = overhaul_service.get_overhaul_status(
                        overhaul_type,
                        due_date,
                        done_date,
                        date_of_commission=date_of_rel_parsed,
                        meterage_km=meterage
                    )
                    data = {
                        'equipment_id': eq_id,
                        'overhaul_type': overhaul_type,
                        'done_date': done_date,
                        'due_date': due_date,
                        'status': status,
                        'meter_reading': meterage,
                        'hours_reading': total_hours,
                        'description': remarks
                    }
                    if existing:
                        Overhaul.update(existing['overhaul_id'], **data)
                    else:
                        Overhaul.create(**data)
                    stats['overhauls'] = stats.get('overhauls', 0) + 1
                    return True
 
                upsert('OH-I', oh1_done_date, oh1_due_date)
                upsert('OH-II', oh2_done_date, oh2_due_date)
 
            except Exception as e:
                logger.error(f"Error importing overhaul data for equipment {eq_id}: {e}")
            
            # Robust DiscardCriteria import with normalized column names
            discard_years_col = find_col(df.columns, 'vintage', 'year')
            discard_kms_col = find_col(df.columns, 'meterage', 'km')
            dc_years = row.get(discard_years_col) if discard_years_col else None
            dc_kms = row.get(discard_kms_col) if discard_kms_col else None
            if (dc_years is not None and str(dc_years).strip() != '' and not pd.isna(dc_years)) or (dc_kms is not None and str(dc_kms).strip() != '' and not pd.isna(dc_kms)):
                dc_years_val = parse_int(dc_years) if dc_years is not None else 0
                dc_kms_val = parse_int(dc_kms) if dc_kms is not None else 0
                dc = DiscardCriteria(
                    equipment_id=eq_id,
                    criteria_years=dc_years_val,
                    criteria_kms=dc_kms_val
                )
                dc.save()
                stats['discard'] = stats.get('discard', 0) + 1
            
            # Robust TyreMaintenance import
            # Look for TYRE section with DT OF CHANGE, ROTATION, and TYRE CONDITION columns
            tyre_date_col = find_col(df.columns, 'tyre', 'dt', 'change') or find_col(df.columns, 'dt', 'change')
            tyre_rotation_col = find_col(df.columns, 'tyre', 'rotation') or find_col(df.columns, 'rotation')
            tyre_condition_col = find_col(df.columns, 'tyre', 'condition') or find_col(df.columns, 'condition')
            
            def norm_val(val):
                if pd.isna(val): return ''
                return str(val).replace('\n', ' ').replace('\r', ' ').replace('\t', ' ').strip().upper()
            
            tyre_date_val = norm_val(row.get(tyre_date_col)) if tyre_date_col else ''
            tyre_rotation_val = norm_val(row.get(tyre_rotation_col)) if tyre_rotation_col else ''
            tyre_condition_val = norm_val(row.get(tyre_condition_col)) if tyre_condition_col else ''
            
            # Import if we have at least rotation interval from condition column or date
            if (tyre_condition_col and tyre_condition_val not in ['', '-', 'NA']) or (tyre_date_col and tyre_date_val not in ['', '-', 'NA']):
                # Parse date of change
                date_of_change = None
                if tyre_date_val and tyre_date_val not in ['', '-', 'NA']:
                    date_of_change = parse_excel_date(tyre_date_val)
                
                # Parse rotation interval from TYRE CONDITION column (this is the KM interval for rotation)
                rotation_interval_kms = 5000  # Default
                if tyre_condition_val and tyre_condition_val not in ['', '-', 'NA']:
                    # Parse condition value which contains rotation interval (like "5000 KM" or "24,000 KM")
                    rotation_interval_kms = parse_int(tyre_condition_val)
                    if rotation_interval_kms == 0:
                        rotation_interval_kms = 5000  # Fallback to default
                
                # Parse any inspection criteria from condition column if it has years
                condition_kms = 0
                condition_yrs = 0
                if tyre_condition_val and tyre_condition_val not in ['', '-', 'NA']:
                    # Try to parse KM/YRS format if present
                    condition_kms, condition_yrs = parse_km_yrs(tyre_condition_val)
                
                from models import TyreMaintenance
                tm = TyreMaintenance(
                    equipment_id=eq_id,
                    tyre_rotation_kms=rotation_interval_kms,  # This is the rotation interval (from condition column)
                    tyre_condition_kms=condition_kms,  # This is for inspection criteria
                    tyre_condition_years=condition_yrs,  # This is for inspection criteria
                    last_rotation_date=date_of_change,
                    date_of_change=date_of_change
                )
                tm.save()
                stats['tyres'] += 1
            
            # Battery import
            bty_col = find_col(df.columns, 'bty')
            bty_life_col = find_col(df.columns, 'life')
            bty_dtchange_col = find_col(df.columns, 'dt', 'change') or find_col(df.columns, 'dtof', 'change')
            
            # Check if we have battery data (either BTY column or DT OF CHANGE)
            bty_val = norm_val(row.get(bty_col)) if bty_col else ''
            bty_dtchange_val = norm_val(row.get(bty_dtchange_col)) if bty_dtchange_col else ''
            bty_life_val = norm_val(row.get(bty_life_col)) if bty_life_col else ''
            
            # Import battery if we have either BTY or DT OF CHANGE data
            if (bty_val and bty_val not in ['', '-', 'NA']) or (bty_dtchange_val and bty_dtchange_val not in ['', '-', 'NA']):
                done_date = None
                custom_life_months = None
                
                # Parse DT OF CHANGE if available
                if bty_dtchange_val and bty_dtchange_val not in ['', '-', 'NA']:
                    done_date = parse_excel_date(bty_dtchange_val)
                
                # Parse LIFE column if available
                if bty_life_val and bty_life_val not in ['', '-', 'NA']:
                    # Parse custom life like "24 MONTHS" or "30 MONTH"
                    bty_life_upper = bty_life_val.upper()
                    if 'MONTH' in bty_life_upper:
                        try:
                            # Remove both MONTH and MONTHS
                            months_str = bty_life_upper.replace('MONTHS', '').replace('MONTH', '').strip()
                            custom_life_months = int(months_str)
                        except ValueError:
                            logger.warning(f"Could not parse custom life months from '{bty_life_val}'")
                
                # Create battery record
                battery = Battery(
                    equipment_id=eq_id,
                    done_date=done_date,
                    custom_life_months=custom_life_months
                )
                battery.save()
                stats['batteries'] = stats.get('batteries', 0) + 1
            
            # Import Medium Reset data (MLOH CRITERIA and OVERHAUL CRITERIA)
            # Look for MLOH and OVERHAUL columns
            medium_reset_columns = []
            
            for col in df.columns:
                col_str = str(col).upper()
                # Check for MLOH CRITERIA or OVERHAUL CRITERIA (including typo OVEHAUL)
                if ('MLOH' in col_str and 'CRITERIA' in col_str) or \
                   ('OVEHAUL' in col_str and 'CRITERIA' in col_str) or \
                   ('OVERHAUL' in col_str and 'CRITERIA' in col_str):
                    medium_reset_columns.append(col)
            
            # Process each medium reset column found
            for col in medium_reset_columns:
                col_val = row.get(col)
                if pd.notna(col_val) and str(col_val).strip() not in ['', '-', 'NA']:
                    col_val_str = str(col_val).strip().upper()
                    
                    # Extract reset type from column name
                    col_name = str(col)
                    if 'MLOH' in col_name.upper():
                        reset_type = 'MLOH'
                    elif 'OH-I' in col_name.upper():
                        reset_type = 'OH-I'
                    elif 'OH-II' in col_name.upper():
                        reset_type = 'OH-II'
                    else:
                        reset_type = 'Medium Reset'
                    
                    done_date = None
                    due_date = None
                    status = None
                    
                    # Parse the value
                    if col_val_str == 'DONE':
                        status = 'Completed'
                    elif col_val_str == 'DUE':
                        status = 'Due'
                    else:
                         # Try to parse as date
                         parsed_date = parse_excel_date(col_val)
                         if parsed_date:
                             # Determine if this is a done date or due date based on context
                             # If the column name suggests it's a due date or the date is in the future, treat as due
                             from datetime import date, datetime
                             
                             # Convert parsed_date to date object if it's a datetime
                             if isinstance(parsed_date, datetime):
                                 parsed_date = parsed_date.date()
                             elif isinstance(parsed_date, str):
                                 # If it's still a string, try to parse it again
                                 try:
                                     parsed_date = datetime.strptime(parsed_date, '%Y-%m-%d').date()
                                 except:
                                     # If parsing fails, skip this entry
                                     continue
                             
                             if isinstance(parsed_date, date) and parsed_date > date.today():
                                 due_date = parsed_date
                                 status = 'Pending'
                             else:
                                 done_date = parsed_date
                                 status = 'Completed'
                    
                    # Create medium reset record if we have meaningful data
                    if done_date or due_date or status:
                        medium_reset = MediumReset(
                             equipment_id=eq_id,
                             reset_type=reset_type,
                             done_date=done_date,
                             due_date=due_date,
                             status=status,
                             meter_reading=meterage_raw if 'meterage_raw' in locals() else None,
                             hours_reading=total_hrs if 'total_hrs' in locals() else None,
                             description=f"Imported from Excel - {col_name}"
                         )
                        medium_reset.save()
                        stats['medium_resets'] = stats.get('medium_resets', 0) + 1
                        logger.debug(f"Created medium reset record: type={reset_type}, done_date={done_date}, due_date={due_date}, status={status}, column={col_name}")
            
            for repair_col in ['Battery Major Repair', 'Misc Repair']:
                desc = row.get(repair_col)
                if pd.notna(desc):
                    repair = Repair(
                        equipment_id=eq_id,
                        repair_type=repair_col,
                        description=str(desc),
                        repair_date=date.today()
                    )
                    repair.save()
                    stats['repairs'] += 1
    
    # After import, update any existing maintenance records that might have wrong categories
    update_maintenance_categories()
    
    return stats

def update_maintenance_categories():
    """Update existing maintenance records to have correct categories based on their type."""
    try:
        from database import execute_query
        
        # Map maintenance types to categories (same as used during import)
        category_updates = [
            ("UPDATE maintenance SET maintenance_category = 'TM-1' WHERE maintenance_type LIKE 'TM-I%' AND (maintenance_category IS NULL OR maintenance_category = '')", 'TM-I'),
            ("UPDATE maintenance SET maintenance_category = 'TM-2' WHERE maintenance_type LIKE 'TM-II%' AND (maintenance_category IS NULL OR maintenance_category = '')", 'TM-II'),
            ("UPDATE maintenance SET maintenance_category = 'TM-1' WHERE maintenance_type LIKE 'TM-III%' AND (maintenance_category IS NULL OR maintenance_category = '')", 'TM-III'),
            ("UPDATE maintenance SET maintenance_category = 'TM-1' WHERE maintenance_type LIKE 'TM-IV%' AND (maintenance_category IS NULL OR maintenance_category = '')", 'TM-IV'),
            ("UPDATE maintenance SET maintenance_category = 'TM-1' WHERE maintenance_type = 'TM-1' AND (maintenance_category IS NULL OR maintenance_category = '')", 'TM-1'),
            ("UPDATE maintenance SET maintenance_category = 'TM-2' WHERE maintenance_type = 'TM-2' AND (maintenance_category IS NULL OR maintenance_category = '')", 'TM-2'),
            ("UPDATE maintenance SET maintenance_category = 'Yearly' WHERE maintenance_type LIKE '%Annual%' AND (maintenance_category IS NULL OR maintenance_category = '')", 'Annual'),
            ("UPDATE maintenance SET maintenance_category = 'Monthly' WHERE maintenance_type LIKE '%Monthly%' AND (maintenance_category IS NULL OR maintenance_category = '')", 'Monthly'),
        ]
        
        for query, type_name in category_updates:
            execute_query(query)
        
    except Exception as e:
        logger.error(f"Error updating maintenance categories: {e}")

def test_excel_column_detection(file_path):
    """Test function to debug Excel column detection for troubleshooting."""
    import pandas as pd
    
    print(f"\n=== TESTING EXCEL COLUMN DETECTION FOR: {file_path} ===")
    
    try:
        xls = pd.ExcelFile(file_path)
        print(f"Found {len(xls.sheet_names)} sheets: {xls.sheet_names}")
        
        for sheet_name in xls.sheet_names[:1]:  # Test first sheet only
            print(f"\n--- ANALYZING SHEET: {sheet_name} ---")
            
            # Read with 3 header rows and flatten columns
            temp = pd.read_excel(xls, sheet_name=sheet_name, header=[0,1,2])
            original_columns = [' '.join([str(x) for x in col if str(x) != 'nan']).strip() for col in temp.columns.values]
            print(f"Original columns ({len(original_columns)}): {original_columns}")
            
            # Apply normalization
            def norm_col(col):
                return re.sub(r'[^a-z0-9]', '', str(col).replace('\n', ' ').replace('\r', ' ').replace('\t', ' ').lower())
            
            normalized_columns = [norm_col(c) for c in original_columns]
            print(f"Normalized columns: {normalized_columns}")
            
            # Test specific pattern matching
            def find_col(cols, *keywords):
                for c in cols:
                    c_low = c.lower()
                    if all(k in c_low for k in keywords):
                        return c
                return None
            
            print(f"\n--- PATTERN MATCHING TESTS ---")
            
            # Test date patterns
            date_patterns = [
                ('date', 'rel'),
                ('dt', 'rel'),
                ('date', 'of', 'rel'),
                ('dt', 'of', 'rel'),
                ('dateofrel',),
                ('dtofrel',),
                ('release', 'date'),
                ('commission', 'date')
            ]
            
            print("Date pattern matching:")
            for pattern in date_patterns:
                result = find_col(normalized_columns, *pattern)
                if result:
                    orig_idx = normalized_columns.index(result)
                    orig_col = original_columns[orig_idx]
                    print(f"  Pattern {pattern}: FOUND -> '{result}' (original: '{orig_col}')")
                else:
                    print(f"  Pattern {pattern}: NOT FOUND")
            
            # Test KM patterns
            km_patterns = [
                ('km', 'run'),
                ('kms', 'run'),
                ('kmrun',),
                ('current', 'km'),
                ('total', 'km')
            ]
            
            print("\nKM pattern matching:")
            for pattern in km_patterns:
                result = find_col(normalized_columns, *pattern)
                if result:
                    orig_idx = normalized_columns.index(result)
                    orig_col = original_columns[orig_idx]
                    print(f"  Pattern {pattern}: FOUND -> '{result}' (original: '{orig_col}')")
                else:
                    print(f"  Pattern {pattern}: NOT FOUND")
            
            # Test HRS patterns
            hrs_patterns = [
                ('hrs', 'run'),
                ('hours', 'run'),
                ('hrsrun',),
                ('current', 'hrs'),
                ('total', 'hrs')
            ]
            
            print("\nHRS pattern matching:")
            for pattern in hrs_patterns:
                result = find_col(normalized_columns, *pattern)
                if result:
                    orig_idx = normalized_columns.index(result)
                    orig_col = original_columns[orig_idx]
                    print(f"  Pattern {pattern}: FOUND -> '{result}' (original: '{orig_col}')")
                else:
                    print(f"  Pattern {pattern}: NOT FOUND")
            
            # Look for any columns containing key terms
            print(f"\n--- COLUMNS CONTAINING KEY TERMS ---")
            key_terms = ['date', 'rel', 'km', 'hrs', 'run', 'commission', 'induction']
            for term in key_terms:
                matching_cols = [col for col in original_columns if term.lower() in col.lower()]
                if matching_cols:
                    print(f"Columns containing '{term}': {matching_cols}")
            
            # Show first few rows of data for context
            print(f"\n--- SAMPLE DATA (First 3 rows) ---")
            df = temp
            if not df.empty:
                for i, row in df.head(3).iterrows():
                    print(f"Row {i}: {dict(zip(original_columns, row.values))}")
            
            break  # Only analyze first sheet
            
    except Exception as e:
        print(f"Error during test: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"=== END TEST ===\n")

# Add this at the end of the file for debugging
if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1:
        test_excel_column_detection(sys.argv[1])
    else:
        print("Usage: python excel_importer.py <excel_file_path>")