"""
Updated Excel import utility for InventoryTracker application.
Optimized for new Excel format with three-header structure.
"""
import pandas as pd
import logging
import re
from datetime import date
from collections import defaultdict
import numpy as np
from dateutil import parser as date_parser

def ensure_date_object(date_value):
    """Convert any date representation to a datetime.date object."""
    if date_value is None:
        return None
    if isinstance(date_value, date):
        return date_value
    try:
        if isinstance(date_value, str):
            return date.fromisoformat(date_value.split(' ')[0])
        elif isinstance(date_value, pd.Timestamp):
            return date_value.date()
        else:
            return date_value.date() if hasattr(date_value, 'date') else None
    except Exception as e:
        logging.debug(f"Could not convert {date_value} to date: {e}")
        return None

logger = logging.getLogger('excel_importer')

def parse_excel_date(val):
    """Enhanced date parser for Excel data."""
    if pd.isna(val) or str(val).strip() in ('', '#######', '-', 'NA', 'N/A', 'nan', 'NaN'):
        return None
    
    try:
        # Handle pandas Timestamp objects directly
        if isinstance(val, pd.Timestamp):
            return val.strftime('%Y-%m-%d')
        
        # Handle Python date objects
        if isinstance(val, date):
            return val.strftime('%Y-%m-%d')
        
        # Handle numeric values (Excel serial numbers)
        if isinstance(val, (int, float)):
            if 1 <= val <= 2958465:  # Valid Excel date range
                try:
                    excel_epoch = pd.Timestamp('1899-12-30')
                    parsed_date = excel_epoch + pd.Timedelta(days=val)
                    return parsed_date.strftime('%Y-%m-%d')
                except:
                    pass
        
        # Handle string values
        s = str(val).replace('\\', '/').replace('.', '/').replace('-', '/').strip().upper()
        
        # Skip obvious non-date strings
        if any(keyword in s for keyword in ['UNDER', 'MLOH', 'YRS', 'YEAR', 'MONTH', 'NA', 'NULL']):
            return None
        
        # Handle DD/MM/YYYY format
        if '/' in s and len(s.split('/')) == 3:
            parts = s.split('/')
            try:
                if len(parts[0]) <= 2 and len(parts[1]) <= 2 and len(parts[2]) >= 2:
                    # Fix common typos
                    if len(parts[2]) > 4:
                        parts[2] = parts[2][:4]
                    
                    day, month, year = int(parts[0]), int(parts[1]), int(parts[2])
                    
                    # Convert 2-digit years
                    if year < 100:
                        year = 2000 + year if year <= 30 else 1900 + year
                    
                    # Validate date components
                    if 1 <= day <= 31 and 1 <= month <= 12 and 1900 <= year <= 2100:
                        try:
                            test_date = date(year, month, day)
                            return test_date.strftime('%Y-%m-%d')
                        except ValueError:
                            return None
            except (ValueError, IndexError):
                pass
        
        # Try dateutil as fallback
        try:
            parsed_date = date_parser.parse(s, dayfirst=True)
            if 1900 <= parsed_date.year <= 2100:
                return parsed_date.strftime('%Y-%m-%d')
        except:
            pass
            
        return None
        
    except Exception as e:
        logger.debug(f"Could not parse date '{val}': {e}")
        return None

def parse_int(val):
    """Parse integer values with enhanced handling."""
    if pd.isna(val) or str(val).strip() in ('', '#######', '-', 'NA', 'N/A'):
        return 0
    s = str(val).replace(',', '').upper()
    
    # Handle range formats like "8 to 10 Year"
    if ' TO ' in s:
        try:
            parts = s.replace('YEAR', '').replace('YRS', '').replace('YR', '').split(' TO ')
            if len(parts) == 2:
                start = float(re.sub(r'[^0-9\.-]', '', parts[0].strip()))
                end = float(re.sub(r'[^0-9\.-]', '', parts[1].strip()))
                return int((start + end) / 2)
        except Exception:
            pass
    
    # Handle complex formats like "5000 Hrs or 105000"
    if ' OR ' in s:
        try:
            parts = s.split(' OR ')
            numbers = []
            for part in parts:
                num_str = re.sub(r'[^0-9\.-]', '', part.strip())
                if num_str:
                    numbers.append(float(num_str))
            if numbers:
                return int(max(numbers))
        except Exception:
            pass
    
    if 'YR' in s:
        s = s.split('YR')[0]
    if 'KM' in s:
        s = s.split('KM')[0]
    try:
        return int(float(s))
    except Exception:
        return 0

def parse_periodicity(val):
    """Parse periodicity format like '800/50/06' (KM/HRS/MONTH)."""
    if pd.isna(val) or str(val).strip() in ('', '#######', '-', 'NA', 'N/A'):
        return 0, 0, 0
    
    s = str(val).strip()
    
    # Handle format like "800/50/06"
    if '/' in s:
        try:
            parts = s.split('/')
            if len(parts) >= 3:
                km = int(parts[0]) if parts[0].isdigit() else 0
                hrs = int(parts[1]) if parts[1].isdigit() else 0
                months = int(parts[2]) if parts[2].isdigit() else 0
                return km, hrs, months
            elif len(parts) == 2:
                # Assume KM/HRS format
                km = int(parts[0]) if parts[0].isdigit() else 0
                hrs = int(parts[1]) if parts[1].isdigit() else 0
                return km, hrs, 0
        except Exception:
            pass
    
    # Try to extract single number
    try:
        return int(float(re.sub(r'[^0-9\.-]', '', s))), 0, 0
    except Exception:
        return 0, 0, 0

def import_from_excel(file_path):
    """
    Import data from an Excel file into the database.
    Updated for new Excel format with three-header structure.
    Returns a dict with import statistics.
    """
    # Import models here to avoid database access during module import
    from models import Equipment, Fluid, DiscardCriteria, TyreMaintenance, Repair, Maintenance, Overhaul, Battery, MediumReset

    logger.info(f"Starting Excel import from {file_path}")

    xls = pd.ExcelFile(file_path)
    existing_equip = Equipment.get_all() or []
    serial_counter = len(existing_equip) + 1
    stats = {
        'equipment': 0,
        'fluids': 0,
        'tyres': 0,
        'batteries': 0,
        'repairs': 0,
        'medium_resets': 0,
        'skipped': 0,
        'overhauls': 0
    }
    
    for sheet_name in xls.sheet_names:
        logger.info(f"Processing sheet: {sheet_name}")
        
        # Read with 3 header rows and flatten columns (new format)
        temp = pd.read_excel(xls, sheet_name=sheet_name, header=[0,1,2])
        
        # Flatten the multi-level columns
        if isinstance(temp.columns, pd.MultiIndex):
            flattened_columns = []
            for col in temp.columns.values:
                # Join non-nan parts and clean up
                col_parts = [str(x) for x in col if str(x) != 'nan' and str(x).strip() != '']
                flattened_name = ' '.join(col_parts).strip()
                flattened_columns.append(flattened_name)
            temp.columns = flattened_columns
        
        df = temp
        
        # Enhanced normalization for new format
        def norm_col(col):
            return re.sub(r'[^a-z0-9]', '', str(col).replace('\n', ' ').replace('\r', ' ').replace('\t', ' ').lower())
        
        normed_cols = {norm_col(c): c for c in df.columns}
        df.columns = [norm_col(c) for c in df.columns]
        
        # Helper to robustly find columns by keywords (updated for new format)
        def find_col(cols, *keywords):
            for c in cols:
                c_low = c.lower()
                if all(k in c_low for k in keywords):
                    return c
            return None
        
        # Updated column mapping for new Excel format
        col_map = {}
        
        # Basic equipment columns
        col_map['serial'] = find_col(df.columns, 'ser', 'no') or find_col(df.columns, 'serial')
        col_map['make_and_type'] = find_col(df.columns, 'make', 'type')
        col_map['ba_number'] = find_col(df.columns, 'ba', 'no')
        col_map['date_of_rel'] = find_col(df.columns, 'date', 'rel') or find_col(df.columns, 'dateofrel')
        col_map['km_run'] = find_col(df.columns, 'km', 'run') or find_col(df.columns, 'kmrun')
        col_map['hrs_run'] = find_col(df.columns, 'hrs', 'run') or find_col(df.columns, 'hrsrun')
        col_map['vintage'] = find_col(df.columns, 'vintage')
        col_map['remarks'] = find_col(df.columns, 'remark')
        
        # Maintenance columns (new format with TM-I, TM-II)
        maintenance_cols = {}
        maintenance_cols['tm1_done'] = find_col(df.columns, 'tm', 'i', 'done') or find_col(df.columns, 'tmi', 'done')
        maintenance_cols['tm1_due'] = find_col(df.columns, 'tm', 'i', 'due') or find_col(df.columns, 'tmi', 'due')
        maintenance_cols['tm2_done'] = find_col(df.columns, 'tm', 'ii', 'done') or find_col(df.columns, 'tmii', 'done')
        maintenance_cols['tm2_due'] = find_col(df.columns, 'tm', 'ii', 'due') or find_col(df.columns, 'tmii', 'due')
        
        # Battery columns (new format)
        battery_cols = {}
        battery_cols['bty_change'] = find_col(df.columns, 'bty', 'dt', 'change') or find_col(df.columns, 'bty', 'change')
        battery_cols['bty_life'] = find_col(df.columns, 'bty', 'life')
        
        # Enhanced debugging for new format
        logger.info(f"=== EXCEL IMPORT DEBUG INFO FOR SHEET: {sheet_name} ===")
        logger.info(f"Total columns found: {len(df.columns)}")
        logger.info(f"Column mapping results:")
        for key, val in col_map.items():
            orig_col = normed_cols.get(val, 'NOT FOUND') if val else 'NOT FOUND'
            logger.info(f"  {key}: '{val}' -> '{orig_col}'")
        logger.info(f"Maintenance columns:")
        for key, val in maintenance_cols.items():
            orig_col = normed_cols.get(val, 'NOT FOUND') if val else 'NOT FOUND'
            logger.info(f"  {key}: '{val}' -> '{orig_col}'")
        logger.info(f"=== END DEBUG INFO ===")
        
        # Process each row
        for _, row in df.iterrows():
            # Get equipment basic info
            make_and_type = row.get(col_map.get('make_and_type', ''), '')
            if not make_and_type or pd.isna(make_and_type) or str(make_and_type).strip() == '':
                stats['skipped'] += 1
                continue
            
            # Get serial number or generate one
            serial_raw = row.get(col_map.get('serial', ''), '')
            if serial_raw and not pd.isna(serial_raw):
                serial = str(serial_raw).strip()
            else:
                serial = str(serial_counter)
                serial_counter += 1
            
            # Extract other equipment data
            ba_number = row.get(col_map.get('ba_number', ''), '')
            if ba_number and not pd.isna(ba_number):
                ba_number = str(ba_number).strip()
            else:
                ba_number = None
            
            # Parse dates
            date_of_rel_raw = row.get(col_map.get('date_of_rel', ''), None)
            date_of_rel_parsed = None
            if date_of_rel_raw and not pd.isna(date_of_rel_raw):
                if isinstance(date_of_rel_raw, pd.Timestamp):
                    date_of_rel_parsed = date_of_rel_raw.strftime('%Y-%m-%d')
                else:
                    date_of_rel_parsed = parse_excel_date(date_of_rel_raw)
            
            # Parse numeric data
            km_run_raw = row.get(col_map.get('km_run', ''), 0)
            hrs_run_raw = row.get(col_map.get('hrs_run', ''), 0)
            vintage_raw = row.get(col_map.get('vintage', ''), 0)
            
            km_run = parse_int(km_run_raw) if km_run_raw not in ['', 'nan', '-', None] and not pd.isna(km_run_raw) else 0
            hrs_run = parse_int(hrs_run_raw) if hrs_run_raw not in ['', 'nan', '-', None] and not pd.isna(hrs_run_raw) else 0
            vintage = parse_int(vintage_raw) if vintage_raw not in ['', 'nan', '-', None] and not pd.isna(vintage_raw) else 0
            
            remarks = str(row.get(col_map.get('remarks', ''), '')).strip()
            
            logger.debug(f"Processing equipment: {make_and_type}, Serial: {serial}, BA: {ba_number}")
            
            # Create or update equipment record
            existing = Equipment.get_by_serial(str(serial))
            if existing:
                eq = Equipment(
                    equipment_id=existing['equipment_id'],
                    serial_number=str(serial),
                    make_and_type=str(make_and_type).strip(),
                    ba_number=ba_number,
                    units_held=1,  # Default to 1
                    vintage_years=vintage,
                    meterage_kms=km_run,  # Use KM RUN as meterage
                    km_hrs_run_previous_month=km_run,
                    km_hrs_run_current_month=km_run,
                    hours_run_total=hrs_run,
                    hours_run_previous_month=0,
                    hours_run_current_month=hrs_run,
                    is_active=True,
                    remarks=remarks,
                    date_of_commission=date_of_rel_parsed
                )
            else:
                eq = Equipment(
                    serial_number=str(serial),
                    make_and_type=str(make_and_type).strip(),
                    ba_number=ba_number,
                    units_held=1,  # Default to 1
                    vintage_years=vintage,
                    meterage_kms=km_run,  # Use KM RUN as meterage
                    km_hrs_run_previous_month=km_run,
                    km_hrs_run_current_month=km_run,
                    hours_run_total=hrs_run,
                    hours_run_previous_month=0,
                    hours_run_current_month=hrs_run,
                    is_active=True,
                    remarks=remarks,
                    date_of_commission=date_of_rel_parsed
                )
            
            eq_id = eq.save()
            if not eq_id:
                logger.warning(f"Failed to save equipment: serial='{serial}', type='{make_and_type}'")
                stats['skipped'] += 1
                continue
            stats['equipment'] += 1
            
            # Enhanced Fluid Import for new format
            fluid_mappings = {
                'engoil': {
                    'keywords': ['eng', 'oil'],
                    'display_name': 'Engine Oil'
                },
                'hydraulicfluid': {
                    'keywords': ['hydraulic', 'fluid'],
                    'display_name': 'Hydraulic Fluid'
                },
                'coolant': {
                    'keywords': ['coolant'],
                    'display_name': 'Coolant'
                },
                'grease_roadwheels': {
                    'keywords': ['road', 'wheels', 'grease'],
                    'display_name': 'Road Wheels Grease'
                },
                'grease_engine_clutch': {
                    'keywords': ['grease', 'engine', 'clutch'],
                    'display_name': 'Engine Clutch Grease'
                }
            }
            
            for fluid_key, fluid_info in fluid_mappings.items():
                # Find columns for this fluid type
                fluid_cols = {}
                for col in df.columns:
                    col_orig = normed_cols.get(col, col)
                    col_lower = col_orig.lower()
                    
                    # Check if this column matches the fluid type
                    if all(keyword in col_lower for keyword in fluid_info['keywords']):
                        if 'capacity' in col_lower:
                            fluid_cols['capacity'] = col
                        elif 'addl' in col_lower or 'top up' in col_lower:
                            fluid_cols['addl'] = col
                        elif 'grade' in col_lower:
                            fluid_cols['grade'] = col
                        elif 'dt' in col_lower and 'change' in col_lower:
                            fluid_cols['date_change'] = col
                        elif 'periodicity' in col_lower:
                            fluid_cols['periodicity'] = col
                
                # Process fluid data if we found relevant columns
                if fluid_cols:
                    capacity = row.get(fluid_cols.get('capacity')) if 'capacity' in fluid_cols else None
                    addl = row.get(fluid_cols.get('addl')) if 'addl' in fluid_cols else None
                    grade = row.get(fluid_cols.get('grade')) if 'grade' in fluid_cols else None
                    date_change = row.get(fluid_cols.get('date_change')) if 'date_change' in fluid_cols else None
                    periodicity = row.get(fluid_cols.get('periodicity')) if 'periodicity' in fluid_cols else None
                    
                    # Only create fluid record if we have meaningful data
                    if any([capacity, addl, grade, date_change, periodicity]):
                        def parse_capacity(val):
                            if val is None or pd.isna(val):
                                return 0.0
                            if isinstance(val, (int, float)):
                                return float(val)
                            s = str(val).upper().replace(',', '').strip()
                            s = re.sub(r'[^0-9\.-]', '', s)
                            try:
                                return float(s) if s else 0.0
                            except Exception:
                                return 0.0
                        
                        # Parse date of change
                        parsed_date_of_change = None
                        if date_change and not pd.isna(date_change):
                            if isinstance(date_change, pd.Timestamp):
                                parsed_date_of_change = date_change.strftime('%Y-%m-%d')
                            else:
                                parsed_date_of_change = parse_excel_date(date_change)
                        
                        # Parse periodicity
                        periodicity_km, periodicity_hrs, periodicity_months = 0, 0, 0
                        if periodicity and not pd.isna(periodicity):
                            periodicity_km, periodicity_hrs, periodicity_months = parse_periodicity(periodicity)
                        
                        from models import Fluid
                        fluid_obj = Fluid(
                            equipment_id=eq_id,
                            fluid_type=fluid_info['display_name'],
                            capacity_ltrs_kg=parse_capacity(capacity),
                            grade=str(grade).strip() if grade and not pd.isna(grade) else None,
                            addl_10_percent_top_up=parse_capacity(addl),
                            periodicity_km=periodicity_km,
                            periodicity_hrs=periodicity_hrs,
                            periodicity_months=periodicity_months,
                            date_of_change=parsed_date_of_change
                        )
                        fluid_obj.save()
                        stats['fluids'] = stats.get('fluids', 0) + 1
                        logger.debug(f"Created fluid record: {fluid_info['display_name']} for equipment {eq_id}")
            
            # Enhanced Maintenance Import for new format
            maintenance_records = []
            
            # TM-I (6 MONTH)
            tm1_done = row.get(maintenance_cols.get('tm1_done')) if maintenance_cols.get('tm1_done') else None
            tm1_due = row.get(maintenance_cols.get('tm1_due')) if maintenance_cols.get('tm1_due') else None
            
            if tm1_done or tm1_due:
                tm1_done_date = None
                tm1_due_date = None
                
                if tm1_done and not pd.isna(tm1_done):
                    if isinstance(tm1_done, pd.Timestamp):
                        tm1_done_date = tm1_done.strftime('%Y-%m-%d')
                    else:
                        tm1_done_date = parse_excel_date(tm1_done)
                
                if tm1_due and not pd.isna(tm1_due):
                    if isinstance(tm1_due, pd.Timestamp):
                        tm1_due_date = tm1_due.strftime('%Y-%m-%d')
                    else:
                        tm1_due_date = parse_excel_date(tm1_due)
                
                maintenance_records.append({
                    'type': 'TM-I',
                    'category': 'TM-1',
                    'done_date': tm1_done_date,
                    'due_date': tm1_due_date
                })
            
            # TM-II (12 MONTH)
            tm2_done = row.get(maintenance_cols.get('tm2_done')) if maintenance_cols.get('tm2_done') else None
            tm2_due = row.get(maintenance_cols.get('tm2_due')) if maintenance_cols.get('tm2_due') else None
            
            if tm2_done or tm2_due:
                tm2_done_date = None
                tm2_due_date = None
                
                if tm2_done and not pd.isna(tm2_done):
                    if isinstance(tm2_done, pd.Timestamp):
                        tm2_done_date = tm2_done.strftime('%Y-%m-%d')
                    else:
                        tm2_done_date = parse_excel_date(tm2_done)
                
                if tm2_due and not pd.isna(tm2_due):
                    if isinstance(tm2_due, pd.Timestamp):
                        tm2_due_date = tm2_due.strftime('%Y-%m-%d')
                    else:
                        tm2_due_date = parse_excel_date(tm2_due)
                
                maintenance_records.append({
                    'type': 'TM-II',
                    'category': 'TM-2', 
                    'done_date': tm2_done_date,
                    'due_date': tm2_due_date
                })
            
            # Create maintenance records
            for record in maintenance_records:
                if record['done_date'] or record['due_date']:
                    m = Maintenance(
                        equipment_id=eq_id,
                        maintenance_type=record['type'],
                        done_date=record['done_date'],
                        due_date=record['due_date'],
                        vintage_years=vintage,
                        meterage_kms=km_run,
                        maintenance_category=record['category']
                    )
                    m.save()
                    logger.debug(f"Created maintenance record: {record['type']} for equipment {eq_id}")
            
            # Enhanced Battery Import for new format
            bty_change = row.get(battery_cols.get('bty_change')) if battery_cols.get('bty_change') else None
            bty_life = row.get(battery_cols.get('bty_life')) if battery_cols.get('bty_life') else None
            
            if bty_change or bty_life:
                bty_change_date = None
                bty_life_months = None
                
                # Parse battery change date
                if bty_change and not pd.isna(bty_change):
                    if isinstance(bty_change, pd.Timestamp):
                        bty_change_date = bty_change.strftime('%Y-%m-%d')
                    else:
                        bty_change_date = parse_excel_date(bty_change)
                
                # Parse battery life
                if bty_life and not pd.isna(bty_life):
                    bty_life_str = str(bty_life).upper().strip()
                    if 'MONTH' in bty_life_str:
                        try:
                            # Extract number from formats like "24 MONTHS", "36 MONTH"
                            bty_life_months = int(re.findall(r'\d+', bty_life_str)[0])
                        except (IndexError, ValueError):
                            logger.warning(f"Could not parse battery life: {bty_life}")
                    else:
                        try:
                            bty_life_months = int(float(bty_life_str))
                        except ValueError:
                            logger.warning(f"Could not parse battery life: {bty_life}")
                
                # Create battery record
                if bty_change_date or bty_life_months:
                    battery = Battery(
                        equipment_id=eq_id,
                        done_date=bty_change_date,
                        custom_life_months=bty_life_months
                    )
                    battery.save()
                    stats['batteries'] = stats.get('batteries', 0) + 1
                    logger.debug(f"Created battery record for equipment {eq_id}")
    
    logger.info(f"Import completed with stats: {stats}")
    return stats

def update_maintenance_categories():
    """Update existing maintenance records to have correct categories based on their type."""
    try:
        from database import execute_query
        
        # Map maintenance types to categories (same as used during import)
        category_updates = [
            ("UPDATE maintenance SET maintenance_category = 'TM-1' WHERE maintenance_type LIKE 'TM-I%' AND (maintenance_category IS NULL OR maintenance_category = '')", 'TM-I'),
            ("UPDATE maintenance SET maintenance_category = 'TM-2' WHERE maintenance_type LIKE 'TM-II%' AND (maintenance_category IS NULL OR maintenance_category = '')", 'TM-II'),
            ("UPDATE maintenance SET maintenance_category = 'TM-1' WHERE maintenance_type LIKE 'TM-III%' AND (maintenance_category IS NULL OR maintenance_category = '')", 'TM-III'),
            ("UPDATE maintenance SET maintenance_category = 'TM-1' WHERE maintenance_type LIKE 'TM-IV%' AND (maintenance_category IS NULL OR maintenance_category = '')", 'TM-IV'),
            ("UPDATE maintenance SET maintenance_category = 'TM-1' WHERE maintenance_type = 'TM-1' AND (maintenance_category IS NULL OR maintenance_category = '')", 'TM-1'),
            ("UPDATE maintenance SET maintenance_category = 'TM-2' WHERE maintenance_type = 'TM-2' AND (maintenance_category IS NULL OR maintenance_category = '')", 'TM-2'),
            ("UPDATE maintenance SET maintenance_category = 'Yearly' WHERE maintenance_type LIKE '%Annual%' AND (maintenance_category IS NULL OR maintenance_category = '')", 'Annual'),
            ("UPDATE maintenance SET maintenance_category = 'Monthly' WHERE maintenance_type LIKE '%Monthly%' AND (maintenance_category IS NULL OR maintenance_category = '')", 'Monthly'),
        ]
        
        for query, type_name in category_updates:
            execute_query(query)
        
    except Exception as e:
        logger.error(f"Error updating maintenance categories: {e}")

if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
        stats = import_from_excel(file_path)
        print(f"Import completed: {stats}")
    else:
        print("Usage: python excel_importer_updated.py <excel_file_path>") 