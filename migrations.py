"""Simple one-off migrations executed at app start to keep schema up to date."""
import logging
import database

logger = logging.getLogger(__name__)

def run():
    """Run idempotent migrations."""
    try:
        # Ensure 'active' column exists in discard_criteria
        cols = database.execute_query("PRAGMA table_info(discard_criteria)")
        if not any(c.get('name') == 'active' for c in cols):
            database.execute_query(
                "ALTER TABLE discard_criteria ADD COLUMN active INTEGER DEFAULT 1",
                fetchall=False,
            )
            logger.info("Migration: added 'active' column to discard_criteria")
    except Exception as exc:
        logger.error("Migration error: %s", exc)
