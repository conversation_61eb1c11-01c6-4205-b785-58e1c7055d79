"""
Enhanced Policy Service for Discard Criteria Management

This service provides a unified interface for managing discard policies
and evaluating equipment against those policies.
"""

import logging
from typing import List, Dict, Optional, Tuple
from datetime import datetime, date
import json

import models
import database
import config

logger = logging.getLogger(__name__)


class PolicyService:
    """Service for managing discard policies and equipment evaluation."""
    
    @staticmethod
    def get_equipment_discard_status(equipment_id: int) -> Dict:
        """
        Get comprehensive discard status for equipment.
        
        Args:
            equipment_id: Equipment ID to evaluate
            
        Returns:
            Dict with status information
        """
        try:
            # Get equipment data
            equipment = models.Equipment.get_by_id(equipment_id)
            if not equipment:
                return {"status": "error", "message": "Equipment not found"}
            
            # Extract equipment data
            if isinstance(equipment, dict):
                eq_data = equipment
            else:
                eq_data = {
                    'equipment_id': getattr(equipment, 'equipment_id', equipment_id),
                    'make_and_type': getattr(equipment, 'make_and_type', ''),
                    'vintage_years': getattr(equipment, 'vintage_years', 0),
                    'meterage_kms': getattr(equipment, 'meterage_kms', 0),
                    'hours_run_total': getattr(equipment, 'hours_run_total', 0),
                    'ba_number': getattr(equipment, 'ba_number', ''),
                    'date_of_commission': getattr(equipment, 'date_of_commission', None),
                    'equipment_status': getattr(equipment, 'equipment_status', 'active')
                }
            
            # Check if already discarded
            if eq_data.get('equipment_status') == 'discarded':
                return {
                    "status": "discarded",
                    "message": "Equipment already discarded",
                    "equipment_data": eq_data
                }
            
            # Get applicable policy
            policy = PolicyService._get_applicable_policy(equipment_id, eq_data['make_and_type'])
            
            if not policy:
                return {
                    "status": "normal",
                    "message": "No discard policy found",
                    "equipment_data": eq_data
                }
            
            # Evaluate against policy
            evaluation = PolicyService._evaluate_against_policy(eq_data, policy)
            
            return {
                "status": evaluation["status"],
                "message": evaluation["message"],
                "equipment_data": eq_data,
                "policy": policy,
                "evaluation_details": evaluation["details"]
            }
            
        except Exception as e:
            logger.error(f"Error getting equipment discard status for {equipment_id}: {e}")
            return {"status": "error", "message": str(e)}
    
    @staticmethod
    def _get_applicable_policy(equipment_id: int, make_and_type: str) -> Optional[Dict]:
        """Get the applicable discard policy for equipment."""
        try:
            # First, check for equipment-specific policy
            equipment_policy = database.execute_query(
                "SELECT * FROM discard_policy WHERE equipment_id = ? AND active = 1 ORDER BY policy_id DESC LIMIT 1",
                (equipment_id,), fetchall=False
            )
            
            if equipment_policy:
                return equipment_policy
            
            # Then, check for class-based policy
            if make_and_type:
                # Find matching class
                normalized_type = make_and_type.lower().strip()
                
                for pattern in config.DISCARD_POLICY_RULES.keys():
                    if pattern in normalized_type:
                        class_row = database.execute_query(
                            "SELECT class_id FROM discard_policy_class WHERE class_name = ?",
                            (pattern,), fetchall=False
                        )
                        
                        if class_row:
                            class_policy = database.execute_query(
                                "SELECT * FROM discard_policy WHERE class_id = ? AND equipment_id IS NULL AND active = 1 ORDER BY policy_id DESC LIMIT 1",
                                (class_row['class_id'],), fetchall=False
                            )
                            
                            if class_policy:
                                return class_policy
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting applicable policy: {e}")
            return None
    
    @staticmethod
    def _evaluate_against_policy(equipment_data: Dict, policy: Dict) -> Dict:
        """Evaluate equipment against discard policy."""
        try:
            current_vintage = float(equipment_data.get('vintage_years', 0))
            current_kms = float(equipment_data.get('meterage_kms', 0))
            current_hours = float(equipment_data.get('hours_run_total', 0))
            
            policy_years = policy.get('vintage_years') or policy.get('discard_years')
            policy_kms = policy.get('second_oh_kms') or policy.get('first_oh_kms')
            
            reasons = []
            meets_criteria = False
            
            # Check vintage years
            if policy_years and current_vintage >= policy_years:
                meets_criteria = True
                reasons.append(f"Age: {current_vintage:.1f} >= {policy_years} years")
            
            # Check mileage
            if policy_kms and current_kms >= policy_kms:
                meets_criteria = True
                reasons.append(f"Mileage: {current_kms:.0f} >= {policy_kms} km")
            
            # Check special conditions (hours-based equipment)
            special_conditions = policy.get('special_conditions', '')
            if special_conditions and 'HRS' in special_conditions.upper():
                # Extract hours threshold from special conditions
                import re
                hours_match = re.search(r'(\d+)\s*HRS', special_conditions.upper())
                if hours_match:
                    hours_threshold = int(hours_match.group(1))
                    if current_hours >= hours_threshold:
                        meets_criteria = True
                        reasons.append(f"Hours: {current_hours:.0f} >= {hours_threshold} hrs")
            
            status = "meeting_discard_criteria" if meets_criteria else "normal"
            message = "; ".join(reasons) if reasons else "Equipment within normal parameters"
            
            return {
                "status": status,
                "message": message,
                "details": {
                    "current_vintage": current_vintage,
                    "current_kms": current_kms,
                    "current_hours": current_hours,
                    "policy_years": policy_years,
                    "policy_kms": policy_kms,
                    "reasons": reasons
                }
            }
            
        except Exception as e:
            logger.error(f"Error evaluating against policy: {e}")
            return {
                "status": "error",
                "message": str(e),
                "details": {}
            }
    
    @staticmethod
    def get_equipment_due_for_discard() -> List[Dict]:
        """Get all equipment that meets discard criteria, grouped by make/type."""
        try:
            equipment_list = models.Equipment.get_active()
            grouped = {}
            
            for equipment in equipment_list:
                equipment_id = equipment.get('equipment_id')
                make_type = equipment.get('make_and_type', 'Unknown')
                
                # Get discard status
                status_info = PolicyService.get_equipment_discard_status(equipment_id)
                
                if status_info["status"] in ["meeting_discard_criteria", "discarded"]:
                    if make_type not in grouped:
                        grouped[make_type] = {
                            "make_and_type": make_type,
                            "ba_numbers": [],
                            "count": 0,
                            "earliest_comm": equipment.get("date_of_commission"),
                            "status": status_info["status"],
                            "equipment_ids": []
                        }
                    
                    group = grouped[make_type]
                    group["ba_numbers"].append(equipment.get("ba_number", "N/A"))
                    group["equipment_ids"].append(equipment_id)
                    group["count"] += 1
                    
                    # Keep earliest commission date
                    eq_comm = equipment.get("date_of_commission")
                    if eq_comm and (group["earliest_comm"] is None or eq_comm < group["earliest_comm"]):
                        group["earliest_comm"] = eq_comm
            
            return list(grouped.values())
            
        except Exception as e:
            logger.error(f"Error getting equipment due for discard: {e}")
            return []
    
    @staticmethod
    def create_policy_for_equipment_type(equipment_type: str, policy_data: Dict) -> bool:
        """Create or update discard policy for an equipment type."""
        try:
            # Create or get equipment class
            class_row = database.execute_query(
                "SELECT class_id FROM discard_policy_class WHERE class_name = ?",
                (equipment_type,), fetchall=False
            )
            
            if not class_row:
                equipment_class = models.DiscardPolicyClass(
                    class_name=equipment_type,
                    description=f"Equipment class for {equipment_type.title()}"
                )
                class_id = equipment_class.save()
            else:
                class_id = class_row['class_id']
            
            # Deactivate existing policies
            database.execute_query(
                "UPDATE discard_policy SET active = 0 WHERE class_id = ? AND equipment_id IS NULL",
                (class_id,), fetchall=False
            )
            
            # Create new policy
            policy = models.DiscardPolicy(
                class_id=class_id,
                equipment_id=None,
                vintage_years=policy_data.get('discard_years'),
                first_oh_kms=policy_data.get('first_oh_kms'),
                second_oh_kms=policy_data.get('discard_kms'),
                discard_years=policy_data.get('discard_years'),
                discard_criteria=policy_data.get('discard_criteria'),
                conditions=json.dumps(policy_data.get('conditions', {})) if policy_data.get('conditions') else None,
                special_conditions=policy_data.get('special_conditions'),
                active=1
            )
            
            policy_id = policy.save()
            return policy_id is not None
            
        except Exception as e:
            logger.error(f"Error creating policy for equipment type {equipment_type}: {e}")
            return False


# Global instance for easy access
policy_service = PolicyService()
