#!/usr/bin/env python3
"""
Populate Discard Policies <PERSON>

This script populates the discard_policy and discard_policy_class tables
based on the official discard policy table provided.

Run this script once to set up the initial discard policies.
"""

import logging
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import database
import models
import config

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_equipment_classes():
    """Create equipment classes based on the discard policy rules."""
    logger.info("Creating equipment classes...")
    
    created_count = 0
    
    for equipment_type, rules in config.DISCARD_POLICY_RULES.items():
        try:
            # Check if class already exists
            existing_class = models.database.execute_query(
                "SELECT class_id FROM discard_policy_class WHERE class_name = ?",
                (equipment_type,), fetchall=False
            )
            
            if not existing_class:
                # Create new equipment class
                equipment_class = models.DiscardPolicyClass(
                    class_name=equipment_type,
                    description=f"Equipment class for {equipment_type.title()}"
                )
                
                class_id = equipment_class.save()
                if class_id:
                    logger.info(f"Created equipment class: {equipment_type} (ID: {class_id})")
                    created_count += 1
                else:
                    logger.error(f"Failed to create equipment class: {equipment_type}")
            else:
                logger.debug(f"Equipment class already exists: {equipment_type}")
                
        except Exception as e:
            logger.error(f"Error creating equipment class {equipment_type}: {e}")
    
    logger.info(f"Created {created_count} new equipment classes")
    return created_count

def create_discard_policies():
    """Create discard policies based on the rules."""
    logger.info("Creating discard policies...")
    
    created_count = 0
    
    for equipment_type, rules in config.DISCARD_POLICY_RULES.items():
        try:
            # Get the class ID
            class_row = models.database.execute_query(
                "SELECT class_id FROM discard_policy_class WHERE class_name = ?",
                (equipment_type,), fetchall=False
            )
            
            if not class_row:
                logger.error(f"Equipment class not found for: {equipment_type}")
                continue
                
            class_id = class_row['class_id']
            
            # Check if policy already exists for this class
            existing_policy = models.database.execute_query(
                "SELECT policy_id FROM discard_policy WHERE class_id = ? AND equipment_id IS NULL AND active = 1",
                (class_id,), fetchall=False
            )
            
            if not existing_policy:
                # Create discard policy
                discard_policy = models.DiscardPolicy(
                    class_id=class_id,
                    equipment_id=None,  # Class-level policy
                    vintage_years=rules.get('discard_years'),
                    first_oh_kms=rules.get('first_oh_kms'),
                    second_oh_kms=rules.get('discard_kms'),
                    discard_years=rules.get('discard_years'),
                    discard_criteria=f"Years: {rules.get('discard_years', 'N/A')}, KMs: {rules.get('discard_kms', 'N/A')}, Hours: {rules.get('hours', 'N/A')}",
                    conditions=None,  # Could store JSON for complex rules
                    special_conditions=rules.get('special_conditions'),
                    active=1
                )
                
                policy_id = discard_policy.save()
                if policy_id:
                    logger.info(f"Created discard policy for {equipment_type} (Policy ID: {policy_id})")
                    created_count += 1
                else:
                    logger.error(f"Failed to create discard policy for: {equipment_type}")
            else:
                logger.debug(f"Discard policy already exists for: {equipment_type}")
                
        except Exception as e:
            logger.error(f"Error creating discard policy for {equipment_type}: {e}")
    
    logger.info(f"Created {created_count} new discard policies")
    return created_count

def update_equipment_with_policies():
    """Update existing equipment to link with appropriate policies."""
    logger.info("Linking equipment with discard policies...")
    
    updated_count = 0
    
    try:
        # Get all active equipment
        equipment_list = models.Equipment.get_active()
        
        for equipment in equipment_list:
            equipment_id = equipment.get('equipment_id')
            make_and_type = equipment.get('make_and_type', '').lower().strip()
            
            if not make_and_type:
                continue
            
            # Find matching policy class
            matching_class = None
            for equipment_type in config.DISCARD_POLICY_RULES.keys():
                if equipment_type in make_and_type:
                    matching_class = equipment_type
                    break
            
            if matching_class:
                # Get class ID
                class_row = models.database.execute_query(
                    "SELECT class_id FROM discard_policy_class WHERE class_name = ?",
                    (matching_class,), fetchall=False
                )
                
                if class_row:
                    class_id = class_row['class_id']
                    
                    # Check if equipment-specific policy already exists
                    existing_policy = models.database.execute_query(
                        "SELECT policy_id FROM discard_policy WHERE equipment_id = ? AND active = 1",
                        (equipment_id,), fetchall=False
                    )
                    
                    if not existing_policy:
                        # Create equipment-specific policy based on class policy
                        rules = config.DISCARD_POLICY_RULES[matching_class]
                        
                        equipment_policy = models.DiscardPolicy(
                            class_id=class_id,
                            equipment_id=equipment_id,
                            vintage_years=rules.get('discard_years'),
                            first_oh_kms=rules.get('first_oh_kms'),
                            second_oh_kms=rules.get('discard_kms'),
                            discard_years=rules.get('discard_years'),
                            discard_criteria=f"Auto-assigned from {matching_class} class",
                            conditions=None,
                            special_conditions=rules.get('special_conditions'),
                            active=1
                        )
                        
                        policy_id = equipment_policy.save()
                        if policy_id:
                            logger.debug(f"Linked equipment {equipment_id} ({make_and_type}) with policy {matching_class}")
                            updated_count += 1
                        
    except Exception as e:
        logger.error(f"Error updating equipment with policies: {e}")
    
    logger.info(f"Linked {updated_count} equipment items with discard policies")
    return updated_count

def main():
    """Main function to populate discard policies."""
    logger.info("Starting discard policy population...")
    
    try:
        # Initialize database connection
        success, first_run = database.init_connection_pool()
        if not success:
            logger.error("Failed to initialize database connection")
            return False
        
        # Create equipment classes
        classes_created = create_equipment_classes()
        
        # Create discard policies
        policies_created = create_discard_policies()
        
        # Link equipment with policies
        equipment_updated = update_equipment_with_policies()
        
        logger.info("Discard policy population completed successfully!")
        logger.info(f"Summary:")
        logger.info(f"  - Equipment classes created: {classes_created}")
        logger.info(f"  - Discard policies created: {policies_created}")
        logger.info(f"  - Equipment items linked: {equipment_updated}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error during discard policy population: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
