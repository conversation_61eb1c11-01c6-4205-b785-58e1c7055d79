"""Centralised discard-status evaluation helper.

This service replaces the previous scattered logic.  It keeps *overhaul*
behaviour intact while migrating day-to-day evaluation to the new
`discard_policy` table.

Public API
----------
get_status(equipment_id) -> str
    Returns one of: "normal", "meeting_discard_criteria", "discarded"
    (the exact wording should match what `DiscardCriteriaWidget` expects).

list_due() -> list[dict]
    Convenience helper returning rows for the widget.  Each dict has
    keys: ba_number, make_and_type, date_of_commission, status,
    years_threshold, kms_threshold.
"""
from __future__ import annotations

import logging
from typing import List, Dict, Optional

import models  # absolute import from project root

logger = logging.getLogger(__name__)

# ---------------------------------------------------------------------------
# constants / util helpers
# ---------------------------------------------------------------------------

def _safe_int(val):
    try:
        return int(val) if val is not None else None
    except (TypeError, ValueError):
        return None

# ---------------------------------------------------------------------------
# core evaluation logic
# ---------------------------------------------------------------------------

def _check_policy(policy_row: dict, eq_row: dict) -> Optional[str]:
    """Return status string if policy indicates discard, else None."""
    years_policy = _safe_int(policy_row.get("vintage_years"))
    kms_policy = _safe_int(policy_row.get("meterage_km") or policy_row.get("first_oh_kms"))

    # Age-based check
    if years_policy and _safe_int(eq_row.get("vintage_years")) is not None:
        if eq_row["vintage_years"] >= years_policy:
            return "meeting_discard_criteria"

    # KMs / meterage check
    if kms_policy and _safe_int(eq_row.get("meterage_kms")) is not None:
        if eq_row["meterage_kms"] >= kms_policy:
            return "meeting_discard_criteria"

    # TODO: add OH / MR date checks as fields become active
    return None


def get_status(equipment_id: int) -> str:
    """Return discard status for a single equipment.

    Priority:
    1. Overhaul-generated discard_criteria rows (keep legacy flow)
    2. User policies in discard_policy
    3. Normal
    """
    # 1. Overhaul-driven discard status (delegated to overhaul_service)
    try:
        import overhaul_service
        over_status = overhaul_service.get_equipment_overhaul_status(equipment_id)
        if over_status and "discard" in str(over_status).lower():
            return "discarded_from_overhaul"
    except Exception as exc:
        logger.debug("Overhaul status retrieval failed: %s", exc)

    # 2a. Equipment-specific policy
    policy_row = models.database.execute_query(
        "SELECT * FROM discard_policy WHERE equipment_id = ? AND active = 1 ORDER BY policy_id DESC LIMIT 1",
        (equipment_id,), fetchall=False,
    )
    # 2b. Fallback to class / make&Type policy
    if not policy_row:
        eq_row = models.database.execute_query(
            "SELECT make_and_type FROM equipment WHERE equipment_id = ?",
            (equipment_id,), fetchall=False,
        )
        make_type = (eq_row or {}).get("make_and_type")
        if make_type:
            cls = models.database.execute_query(
                "SELECT class_id FROM discard_policy_class WHERE class_name = ?",
                (make_type,), fetchall=False,
            )
            cls_id = (cls or {}).get("class_id")
            if cls_id:
                policy_row = models.database.execute_query(
                    "SELECT * FROM discard_policy WHERE class_id = ? AND equipment_id IS NULL AND active = 1 ORDER BY policy_id DESC LIMIT 1",
                    (cls_id,), fetchall=False,
                )
    if policy_row:
        eq = models.database.execute_query("SELECT * FROM equipment WHERE equipment_id = ?", (equipment_id,), fetchall=False)
        if eq:
            st = _check_policy(policy_row, eq)
            if st:
                return st

    return "normal"

# ---------------------------------------------------------------------------
# list helper for widget
# ---------------------------------------------------------------------------

def list_due() -> List[Dict]:
    """Return grouped rows (one per Make & Type) for widget."""
    grouped: Dict[str, Dict] = {}
    equipments = models.database.execute_query("SELECT * FROM equipment")
    for eq in equipments:
        status = get_status(eq["equipment_id"])
        if status == "normal":
            continue
        mtype = eq.get("make_and_type") or "Unknown"
        g = grouped.setdefault(mtype, {
            "make_and_type": mtype,
            "ba_numbers": [],
            "count": 0,
            "earliest_comm": eq.get("date_of_commission"),
            "status": status,
        })
        g["ba_numbers"].append(eq.get("ba_number"))
        g["count"] += 1
        # keep earliest commission date for info
        if eq.get("date_of_commission") and (g["earliest_comm"] is None or eq["date_of_commission"] < g["earliest_comm"]):
            g["earliest_comm"] = eq.get("date_of_commission")
    return list(grouped.values())
