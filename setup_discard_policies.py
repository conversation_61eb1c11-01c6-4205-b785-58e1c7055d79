#!/usr/bin/env python3
"""
Setup Discard Policies - Complete Setup Script

This script sets up the complete discard policy system:
1. Populates equipment classes and policies
2. Tests the policy service
3. Validates the integration

Run this script to set up your discard policy system according to your official table.
"""

import logging
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import database
import models
import config
from populate_discard_policies import main as populate_policies
from policy_service import policy_service

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_policy_service():
    """Test the policy service functionality."""
    logger.info("Testing policy service...")
    
    try:
        # Get some equipment to test
        equipment_list = models.Equipment.get_active()
        
        if not equipment_list:
            logger.warning("No equipment found for testing")
            return False
        
        test_count = min(5, len(equipment_list))  # Test up to 5 equipment items
        
        for i, equipment in enumerate(equipment_list[:test_count]):
            equipment_id = equipment.get('equipment_id')
            make_type = equipment.get('make_and_type', 'Unknown')
            ba_number = equipment.get('ba_number', 'N/A')
            
            logger.info(f"Testing equipment {i+1}/{test_count}: {ba_number} - {make_type}")
            
            # Test policy service
            status_info = policy_service.get_equipment_discard_status(equipment_id)
            
            logger.info(f"  Status: {status_info.get('status')}")
            logger.info(f"  Message: {status_info.get('message')}")
            
            if status_info.get('policy'):
                policy = status_info['policy']
                logger.info(f"  Policy found: Years={policy.get('vintage_years')}, KMs={policy.get('first_oh_kms')}")
            else:
                logger.info("  No policy found")
        
        # Test grouped listing
        logger.info("Testing grouped equipment listing...")
        grouped_equipment = policy_service.get_equipment_due_for_discard()
        
        logger.info(f"Found {len(grouped_equipment)} equipment groups meeting discard criteria:")
        for group in grouped_equipment:
            logger.info(f"  - {group['make_and_type']}: {group['count']} units, Status: {group['status']}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error testing policy service: {e}")
        return False

def validate_database_schema():
    """Validate that all required tables exist."""
    logger.info("Validating database schema...")
    
    required_tables = [
        'equipment',
        'discard_policy_class', 
        'discard_policy'
    ]
    
    try:
        for table in required_tables:
            result = database.execute_query(
                "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
                (table,), fetchall=False
            )
            
            if result:
                logger.info(f"  ✓ Table '{table}' exists")
            else:
                logger.error(f"  ✗ Table '{table}' missing")
                return False
        
        return True
        
    except Exception as e:
        logger.error(f"Error validating schema: {e}")
        return False

def show_policy_summary():
    """Show a summary of created policies."""
    logger.info("Policy Summary:")
    
    try:
        # Count equipment classes
        classes = database.execute_query("SELECT COUNT(*) as count FROM discard_policy_class")
        class_count = classes[0]['count'] if classes else 0
        logger.info(f"  Equipment Classes: {class_count}")
        
        # Count policies
        policies = database.execute_query("SELECT COUNT(*) as count FROM discard_policy WHERE active = 1")
        policy_count = policies[0]['count'] if policies else 0
        logger.info(f"  Active Policies: {policy_count}")
        
        # Show some example policies
        example_policies = database.execute_query("""
            SELECT dp.*, dpc.class_name 
            FROM discard_policy dp
            JOIN discard_policy_class dpc ON dp.class_id = dpc.class_id
            WHERE dp.active = 1 AND dp.equipment_id IS NULL
            LIMIT 5
        """)
        
        logger.info("  Example Policies:")
        for policy in example_policies:
            logger.info(f"    - {policy['class_name']}: {policy['vintage_years']} years, {policy['first_oh_kms']} km")
        
        # Count equipment with policies
        equipment_with_policies = database.execute_query("""
            SELECT COUNT(DISTINCT e.equipment_id) as count
            FROM equipment e
            WHERE EXISTS (
                SELECT 1 FROM discard_policy dp 
                WHERE dp.equipment_id = e.equipment_id AND dp.active = 1
            )
        """)
        
        equipment_count = equipment_with_policies[0]['count'] if equipment_with_policies else 0
        logger.info(f"  Equipment with specific policies: {equipment_count}")
        
    except Exception as e:
        logger.error(f"Error showing policy summary: {e}")

def main():
    """Main setup function."""
    logger.info("=" * 60)
    logger.info("DISCARD POLICY SYSTEM SETUP")
    logger.info("=" * 60)
    
    try:
        # Initialize database
        logger.info("Step 1: Initializing database...")
        success, first_run = database.init_connection_pool()
        if not success:
            logger.error("Failed to initialize database")
            return False
        
        # Validate schema
        logger.info("Step 2: Validating database schema...")
        if not validate_database_schema():
            logger.error("Database schema validation failed")
            return False
        
        # Populate policies
        logger.info("Step 3: Populating discard policies...")
        if not populate_policies():
            logger.error("Failed to populate discard policies")
            return False
        
        # Test policy service
        logger.info("Step 4: Testing policy service...")
        if not test_policy_service():
            logger.error("Policy service testing failed")
            return False
        
        # Show summary
        logger.info("Step 5: Showing policy summary...")
        show_policy_summary()
        
        logger.info("=" * 60)
        logger.info("DISCARD POLICY SYSTEM SETUP COMPLETED SUCCESSFULLY!")
        logger.info("=" * 60)
        logger.info("")
        logger.info("Next steps:")
        logger.info("1. Run the main application: python main.py")
        logger.info("2. Navigate to the 'Discard Criteria' tab")
        logger.info("3. Review equipment meeting discard criteria")
        logger.info("4. Use the policy management features")
        logger.info("")
        
        return True
        
    except Exception as e:
        logger.error(f"Setup failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
