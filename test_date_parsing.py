#!/usr/bin/env python3
"""
Test script for the improved date parsing function
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from excel_importer import parse_excel_date

def test_date_parsing():
    """Test the improved date parsing with problematic values from the logs"""
    
    print("🧪 TESTING IMPROVED DATE PARSING FUNCTION")
    print("=" * 60)
    
    # Test cases from the error logs and expected scenarios
    test_cases = [
        # Excel serial numbers (these were causing the "year out of range" errors)
        (45825, "Expected: 2025-06-12 (Excel serial number)"),
        (45676, "Expected: 2025-01-14 (Excel serial number)"),
        (41132, "Expected: 2012-07-23 (Excel serial number)"),
        
        # Valid date formats  
        ("23/07/2000", "Expected: 2000-07-23 (DD/MM/YYYY)"),
        ("21/10/2003", "Expected: 2003-10-21 (DD/MM/YYYY)"),
        
        # Invalid/problematic formats from logs
        ("23/03/20211", "Expected: 2021-03-23 (fix typo)"),
        ("31/06/25", "Expected: None (invalid date)"),
        ("19/03/22 12/05/23", "Expected: 2022-03-19 (take first date)"),
        ("UNDER MLOH", "Expected: None (non-date text)"),
        ("4 Yrs", "Expected: None (non-date text)"),
        
        # Edge cases
        ("", "Expected: None (empty)"),
        ("--", "Expected: None (dashes)"),
        ("NA", "Expected: None (NA)"),
        (None, "Expected: None (None)"),
        
        # 2-digit years
        ("23/07/00", "Expected: 2000-07-23 (2-digit year)"),
        ("15/12/99", "Expected: 1999-12-15 (2-digit year)"),
    ]
    
    print(f"Testing {len(test_cases)} date parsing scenarios...\n")
    
    passed = 0
    failed = 0
    
    for i, (input_val, expected_desc) in enumerate(test_cases, 1):
        print(f"Test {i:2d}: Input: {repr(input_val)}")
        print(f"        {expected_desc}")
        
        try:
            result = parse_excel_date(input_val)
            print(f"        Result: {repr(result)}")
            
            # Basic validation - at least check if we got a reasonable result
            if result is not None:
                if len(result) == 10 and result.count('-') == 2:
                    year = int(result.split('-')[0])
                    if 1900 <= year <= 2100:
                        print(f"        ✅ VALID DATE FORMAT")
                        passed += 1
                    else:
                        print(f"        ❌ INVALID YEAR: {year}")
                        failed += 1
                else:
                    print(f"        ❌ INVALID FORMAT")
                    failed += 1
            else:
                print(f"        ✅ CORRECTLY REJECTED")
                passed += 1
                
        except Exception as e:
            print(f"        ❌ ERROR: {e}")
            failed += 1
        
        print()
    
    print("=" * 60)
    print(f"📊 RESULTS: {passed} passed, {failed} failed out of {len(test_cases)} tests")
    
    if failed == 0:
        print("🎉 All tests passed!")
    else:
        print(f"⚠️  {failed} tests failed - review the function")
    
    return failed == 0

if __name__ == "__main__":
    success = test_date_parsing()
    sys.exit(0 if success else 1) 