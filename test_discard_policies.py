#!/usr/bin/env python3
"""
Test Discard Policies

This script tests the discard policy system with sample data
to verify it works according to your official table.
"""

import logging
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import config
from policy_service import policy_service

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_policy_rules():
    """Test the policy rules configuration."""
    logger.info("Testing policy rules configuration...")
    
    test_cases = [
        # Equipment Type, Expected Years, Expected KMs
        ("MOTOR CYCLE (HH/RE)", 15, 75000),
        ("TATA SAFARI", 9, 100000),
        ("MAHINDRA SCORPIO", 15, 100000),
        ("MARUTI GYPSY", 15, 110000),
        ("BMP-III", 24, 2400),
        ("DOZER D-80", None, None),  # Hours-based
        ("GENERATOR SET UPTO 5KVA", None, None),  # Hours-based
    ]
    
    for equipment_type, expected_years, expected_kms in test_cases:
        logger.info(f"\nTesting: {equipment_type}")
        
        # Check if rule exists
        normalized_type = equipment_type.lower()
        found_rule = None
        
        for pattern, rules in config.DISCARD_POLICY_RULES.items():
            if pattern in normalized_type:
                found_rule = (pattern, rules)
                break
        
        if found_rule:
            pattern, rules = found_rule
            logger.info(f"  ✓ Matched pattern: '{pattern}'")
            logger.info(f"  ✓ Discard years: {rules.get('discard_years')} (expected: {expected_years})")
            logger.info(f"  ✓ Discard KMs: {rules.get('discard_kms')} (expected: {expected_kms})")
            
            if rules.get('special_conditions'):
                logger.info(f"  ✓ Special conditions: {rules['special_conditions']}")
            
            # Validate expectations
            if expected_years and rules.get('discard_years') != expected_years:
                logger.warning(f"  ⚠ Years mismatch: got {rules.get('discard_years')}, expected {expected_years}")
            
            if expected_kms and rules.get('discard_kms') != expected_kms:
                logger.warning(f"  ⚠ KMs mismatch: got {rules.get('discard_kms')}, expected {expected_kms}")
        else:
            logger.warning(f"  ✗ No rule found for: {equipment_type}")

def test_sample_equipment():
    """Test with sample equipment data."""
    logger.info("\nTesting with sample equipment data...")
    
    sample_equipment = [
        {
            "equipment_id": 1,
            "make_and_type": "MOTOR CYCLE (HH/RE)",
            "vintage_years": 16,  # Over 15 year limit
            "meterage_kms": 80000,  # Over 75,000 km limit
            "hours_run_total": 0,
            "ba_number": "86R3124A",
            "equipment_status": "active"
        },
        {
            "equipment_id": 2,
            "make_and_type": "MAHINDRA SCORPIO",
            "vintage_years": 8,  # Under 15 year limit
            "meterage_kms": 50000,  # Under 100,000 km limit
            "hours_run_total": 0,
            "ba_number": "95R5843H",
            "equipment_status": "active"
        },
        {
            "equipment_id": 3,
            "make_and_type": "BMP-III",
            "vintage_years": 25,  # Over 24 year limit
            "meterage_kms": 2500,  # Over 2400 km limit
            "hours_run_total": 0,
            "ba_number": "09E020651H",
            "equipment_status": "active"
        },
        {
            "equipment_id": 4,
            "make_and_type": "GENERATOR SET UPTO 5KVA",
            "vintage_years": 10,
            "meterage_kms": 0,
            "hours_run_total": 8000,  # Over 7000 hour limit
            "ba_number": "12G001234A",
            "equipment_status": "active"
        }
    ]
    
    for equipment in sample_equipment:
        logger.info(f"\nTesting Equipment: {equipment['ba_number']} - {equipment['make_and_type']}")
        logger.info(f"  Current: {equipment['vintage_years']} years, {equipment['meterage_kms']} km, {equipment['hours_run_total']} hrs")
        
        # Find matching rule
        normalized_type = equipment['make_and_type'].lower()
        matching_rule = None
        
        for pattern, rules in config.DISCARD_POLICY_RULES.items():
            if pattern in normalized_type:
                matching_rule = rules
                break
        
        if matching_rule:
            logger.info(f"  Policy: {matching_rule.get('discard_years')} years, {matching_rule.get('discard_kms')} km")
            
            # Check if meets discard criteria
            meets_criteria = False
            reasons = []
            
            # Age check
            if matching_rule.get('discard_years') and equipment['vintage_years'] >= matching_rule['discard_years']:
                meets_criteria = True
                reasons.append(f"Age: {equipment['vintage_years']} >= {matching_rule['discard_years']} years")
            
            # KM check
            if matching_rule.get('discard_kms') and equipment['meterage_kms'] >= matching_rule['discard_kms']:
                meets_criteria = True
                reasons.append(f"Mileage: {equipment['meterage_kms']} >= {matching_rule['discard_kms']} km")
            
            # Hours check (from special conditions)
            if matching_rule.get('hours') and equipment['hours_run_total'] >= matching_rule['hours']:
                meets_criteria = True
                reasons.append(f"Hours: {equipment['hours_run_total']} >= {matching_rule['hours']} hrs")
            
            if meets_criteria:
                logger.info(f"  ✓ MEETS DISCARD CRITERIA: {'; '.join(reasons)}")
            else:
                logger.info(f"  ✓ Within normal parameters")
        else:
            logger.info(f"  ✗ No matching policy found")

def main():
    """Main test function."""
    logger.info("=" * 60)
    logger.info("DISCARD POLICY TESTING")
    logger.info("=" * 60)
    
    try:
        # Test policy rules
        test_policy_rules()
        
        # Test sample equipment
        test_sample_equipment()
        
        logger.info("\n" + "=" * 60)
        logger.info("TESTING COMPLETED")
        logger.info("=" * 60)
        
        return True
        
    except Exception as e:
        logger.error(f"Testing failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
