#!/usr/bin/env python3
"""
Excel Import Debug Tool
This script helps debug Excel column detection issues for the Army Equipment Inventory system.
"""

import pandas as pd
import re
import sys
import os

def test_excel_columns(file_path):
    """Test Excel column detection for troubleshooting import issues."""
    
    if not os.path.exists(file_path):
        print(f"Error: File '{file_path}' not found!")
        return
    
    print(f"\n🔍 ANALYZING EXCEL FILE: {file_path}")
    print("=" * 80)
    
    try:
        xls = pd.ExcelFile(file_path)
        print(f"📊 Found {len(xls.sheet_names)} sheet(s): {xls.sheet_names}")
        
        for sheet_idx, sheet_name in enumerate(xls.sheet_names):
            print(f"\n📋 SHEET {sheet_idx + 1}: '{sheet_name}'")
            print("-" * 50)
            
            # Read with multiple header rows (as used in the main importer)
            try:
                df = pd.read_excel(xls, sheet_name=sheet_name, header=[0,1,2])
                original_columns = [' '.join([str(x) for x in col if str(x) != 'nan']).strip() for col in df.columns.values]
            except:
                # Fallback to single header row
                df = pd.read_excel(xls, sheet_name=sheet_name, header=0)
                original_columns = df.columns.tolist()
            
            print(f"📝 Total columns: {len(original_columns)}")
            print(f"📝 All columns: {original_columns}")
            
            # Normalize columns (same as main importer)
            def norm_col(col):
                return re.sub(r'[^a-z0-9]', '', str(col).replace('\n', ' ').replace('\r', ' ').replace('\t', ' ').lower())
            
            normalized_cols = {norm_col(c): c for c in original_columns}
            norm_col_list = [norm_col(c) for c in original_columns]
            
            print(f"\n🔍 SEARCHING FOR DATE OF RELEASE FIELDS:")
            date_patterns = [
                'dateofrel', 'dtofrel', 'date rel', 'dt rel', 'release date', 'commission date',
                'date commission', 'induction date', 'date induction'
            ]
            
            found_date_cols = []
            for pattern in date_patterns:
                pattern_norm = norm_col(pattern)
                matches = [col for col in norm_col_list if pattern_norm in col or any(word in col for word in pattern_norm.split())]
                if matches:
                    for match in matches:
                        orig_col = normalized_cols[match]
                        found_date_cols.append((pattern, match, orig_col))
                        print(f"  ✅ Pattern '{pattern}' -> Column: '{orig_col}' (normalized: '{match}')")
            
            if not found_date_cols:
                print("  ❌ No date columns found with standard patterns")
                # Look for any column containing 'date' or 'rel'
                date_like = [col for col in original_columns if 'date' in col.lower() or 'rel' in col.lower()]
                if date_like:
                    print(f"  🔍 Found date-like columns: {date_like}")
            
            print(f"\n🔍 SEARCHING FOR KM RUN FIELDS:")
            km_patterns = [
                'km run', 'kms run', 'kmrun', 'kmsrun', 'current km', 'total km', 
                'km reading', 'kilometer reading', 'mileage', 'meterage'
            ]
            
            found_km_cols = []
            for pattern in km_patterns:
                pattern_norm = norm_col(pattern)
                matches = [col for col in norm_col_list if pattern_norm in col or any(word in col for word in pattern_norm.split())]
                if matches:
                    for match in matches:
                        orig_col = normalized_cols[match]
                        found_km_cols.append((pattern, match, orig_col))
                        print(f"  ✅ Pattern '{pattern}' -> Column: '{orig_col}' (normalized: '{match}')")
            
            if not found_km_cols:
                print("  ❌ No KM columns found with standard patterns")
                km_like = [col for col in original_columns if 'km' in col.lower() or 'run' in col.lower()]
                if km_like:
                    print(f"  🔍 Found KM-like columns: {km_like}")
            
            print(f"\n🔍 SEARCHING FOR HRS RUN FIELDS:")
            hrs_patterns = [
                'hrs run', 'hours run', 'hrsrun', 'hoursrun', 'current hrs', 'total hrs',
                'hrs reading', 'hours reading', 'engine hrs', 'engine hours'
            ]
            
            found_hrs_cols = []
            for pattern in hrs_patterns:
                pattern_norm = norm_col(pattern)
                matches = [col for col in norm_col_list if pattern_norm in col or any(word in col for word in pattern_norm.split())]
                if matches:
                    for match in matches:
                        orig_col = normalized_cols[match]
                        found_hrs_cols.append((pattern, match, orig_col))
                        print(f"  ✅ Pattern '{pattern}' -> Column: '{orig_col}' (normalized: '{match}')")
            
            if not found_hrs_cols:
                print("  ❌ No HRS columns found with standard patterns")
                hrs_like = [col for col in original_columns if 'hrs' in col.lower() or 'hour' in col.lower()]
                if hrs_like:
                    print(f"  🔍 Found HRS-like columns: {hrs_like}")
            
            # Show sample data from found columns
            if not df.empty and (found_date_cols or found_km_cols or found_hrs_cols):
                print(f"\n📊 SAMPLE DATA FROM FOUND COLUMNS (First 5 rows):")
                sample_data = {}
                
                # Add date columns
                for _, _, orig_col in found_date_cols[:3]:  # Max 3 date columns
                    if orig_col in df.columns:
                        sample_data[f"DATE: {orig_col}"] = df[orig_col].head(5).tolist()
                
                # Add KM columns  
                for _, _, orig_col in found_km_cols[:3]:  # Max 3 KM columns
                    if orig_col in df.columns:
                        sample_data[f"KM: {orig_col}"] = df[orig_col].head(5).tolist()
                
                # Add HRS columns
                for _, _, orig_col in found_hrs_cols[:3]:  # Max 3 HRS columns
                    if orig_col in df.columns:
                        sample_data[f"HRS: {orig_col}"] = df[orig_col].head(5).tolist()
                
                for col_name, values in sample_data.items():
                    print(f"  {col_name}: {values}")
            
            # Only analyze first sheet for now
            break
            
    except Exception as e:
        print(f"❌ Error analyzing file: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 80)
    print("🎯 RECOMMENDATIONS:")
    print("1. Check if your Excel has multi-level headers (merged cells)")
    print("2. Ensure DATE OF REL column contains actual dates (not text)")  
    print("3. Verify KM RUN and HRS RUN columns have numeric values")
    print("4. Try renaming columns to match expected patterns if needed")
    print("=" * 80)

def main():
    if len(sys.argv) != 2:
        print("Usage: python test_excel_debug.py <path_to_excel_file>")
        print("Example: python test_excel_debug.py equipment_data.xlsx")
        return
    
    excel_file = sys.argv[1]
    test_excel_columns(excel_file)

if __name__ == "__main__":
    main() 