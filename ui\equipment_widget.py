"""Equipment management widget for the equipment inventory application."""
import logging
from datetime import datetime, date
from PyQt5.QtCore import Qt, QDate, QTimer
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, 
                            QLabel, QPushButton, QLineEdit, QComboBox, 
                            QSpinBox, QDoubleSpinBox, QDateEdit, QCheckBox,
                            QGroupBox, QFormLayout, QMessageBox, QSplitter,
                            QTextEdit, QTableWidget, QHeaderView, 
                            QAbstractItemView, QTableWidgetItem, QTabWidget)

import database
import utils
from models import Equipment, Fluid, Maintenance, Repair, DiscardCriteria, TyreMaintenance
from ui.custom_widgets import ReadOnlyTableWidget, StatusLabel

# Configure logger
logger = logging.getLogger('equipment_widget')

class EquipmentLifeWidget(QWidget):
    """Comprehensive equipment life overview widget showing all related data."""
    
    def __init__(self, parent=None):
        """Initialize the Equipment Life widget."""
        super().__init__(parent)
        self.current_equipment_id = None
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the equipment life overview UI."""
        # Create main layout with scrollable area
        main_layout = QVBoxLayout(self)
        
        # Create scroll area for the content
        from PyQt5.QtWidgets import QScrollArea
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # Create content widget and a 2-column grid layout
        content_widget = QWidget()
        grid_layout = QGridLayout(content_widget)
        grid_layout.setSpacing(15)
        grid_layout.setContentsMargins(10, 10, 10, 10)

        # Generate all section widgets
        summary_group = self.create_equipment_summary_section()
        stats_group = self.create_statistics_section()
        maintenance_group = self.create_maintenance_section()
        fluids_group = self.create_fluids_section()
        discard_group = self.create_discard_section()
        demand_group = self.create_demand_section()
        mr_group = self.create_mr_section()
        overhaul_group = self.create_overhaul_section()

        # Row/column placement in a 2x4 grid
        # Full-width (span 2 columns)
        grid_layout.addWidget(summary_group, 0, 0, 1, 2)
        grid_layout.addWidget(stats_group,    1, 0, 1, 2)

        # Two-column grid below
        grid_layout.addWidget(maintenance_group, 2, 0)
        grid_layout.addWidget(fluids_group,      2, 1)
        grid_layout.addWidget(discard_group,     3, 0)
        grid_layout.addWidget(demand_group,      3, 1)
        
        # MR and Overhaul sections (full width)
        grid_layout.addWidget(mr_group,       4, 0, 1, 2)
        grid_layout.addWidget(overhaul_group, 5, 0, 1, 2)

        # Stretch to push everything to the top when there is extra space
        grid_layout.setRowStretch(6, 1)
        
        # Set content widget to scroll area
        scroll_area.setWidget(content_widget)
        main_layout.addWidget(scroll_area)
        
    def create_equipment_summary_section(self):
        """Create and return the equipment summary section."""
        summary_group = QGroupBox("📋 Equipment Summary")
        summary_layout = QGridLayout()

        # Basic info labels
        self.summary_ba_number = QLabel("--")
        self.summary_make_type = QLabel("--")
        self.summary_vintage = QLabel("--")
        self.summary_meterage = QLabel("--")
        self.summary_hours = QLabel("--")
        self.summary_status = QLabel("--")

        # Add to grid
        summary_layout.addWidget(QLabel("BA Number:"), 0, 0)
        summary_layout.addWidget(self.summary_ba_number, 0, 1)
        summary_layout.addWidget(QLabel("Make & Type:"), 0, 2)
        summary_layout.addWidget(self.summary_make_type, 0, 3)

        summary_layout.addWidget(QLabel("Vintage:"), 1, 0)
        summary_layout.addWidget(self.summary_vintage, 1, 1)
        summary_layout.addWidget(QLabel("Total Meterage:"), 1, 2)
        summary_layout.addWidget(self.summary_meterage, 1, 3)

        summary_layout.addWidget(QLabel("Total Hours:"), 2, 0)
        summary_layout.addWidget(self.summary_hours, 2, 1)
        summary_layout.addWidget(QLabel("Status:"), 2, 2)
        summary_layout.addWidget(self.summary_status, 2, 3)

        summary_group.setLayout(summary_layout)
        return summary_group

    def create_statistics_section(self):
        """Create and return the statistics section."""
        stats_group = QGroupBox("📊 Usage Statistics")
        stats_layout = QGridLayout()

        # Statistics labels
        self.stats_monthly_km = QLabel("--")
        self.stats_monthly_hrs = QLabel("--")
        self.stats_efficiency = QLabel("--")
        self.stats_utilization = QLabel("--")

        stats_layout.addWidget(QLabel("Avg Monthly KM:"), 0, 0)
        stats_layout.addWidget(self.stats_monthly_km, 0, 1)
        stats_layout.addWidget(QLabel("Avg Monthly Hrs:"), 0, 2)
        stats_layout.addWidget(self.stats_monthly_hrs, 0, 3)

        stats_layout.addWidget(QLabel("Efficiency:"), 1, 0)
        stats_layout.addWidget(self.stats_efficiency, 1, 1)
        stats_layout.addWidget(QLabel("Utilization:"), 1, 2)
        stats_layout.addWidget(self.stats_utilization, 1, 3)

        stats_group.setLayout(stats_layout)
        return stats_group

    def create_maintenance_section(self):
        """Create and return the maintenance history section."""
        maintenance_group = QGroupBox("🔧 Maintenance Overview")
        maintenance_layout = QVBoxLayout()
        
        # Maintenance summary with action button
        summary_layout = QHBoxLayout()
        self.maintenance_completed = QLabel("Completed: --")
        self.maintenance_pending = QLabel("Pending: --")
        self.maintenance_overdue = QLabel("Overdue: --")
        
        summary_layout.addWidget(self.maintenance_completed)
        summary_layout.addWidget(self.maintenance_pending)
        summary_layout.addWidget(self.maintenance_overdue)
        summary_layout.addStretch()
        
        # Add button to view in main maintenance tab
        self.view_maintenance_btn = QPushButton("🔗 View in Maintenance Tab")
        self.view_maintenance_btn.setMaximumWidth(180)
        self.view_maintenance_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        self.view_maintenance_btn.clicked.connect(self.open_in_maintenance_tab)
        summary_layout.addWidget(self.view_maintenance_btn)
        
        maintenance_layout.addLayout(summary_layout)
        
        # Recent maintenance table
        from ui.custom_widgets import ReadOnlyTableWidget
        self.maintenance_table = ReadOnlyTableWidget()
        self.maintenance_table.setMaximumHeight(600)  # Increased to 600 for more entries
        maintenance_layout.addWidget(self.maintenance_table)
        
        maintenance_group.setLayout(maintenance_layout)
        return maintenance_group

    def create_fluids_section(self):
        """Create and return the fluids section."""
        fluids_group = QGroupBox("🛢️ Fluids & Consumables")
        fluids_layout = QVBoxLayout()
        
        # Fluids table
        self.fluids_table = ReadOnlyTableWidget()
        self.fluids_table.setMaximumHeight(600)  # Increased to 600 for more entries
        fluids_layout.addWidget(self.fluids_table)
        
        fluids_group.setLayout(fluids_layout)
        return fluids_group

    def create_discard_section(self):
        """Create and return the discard criteria section."""
        discard_group = QGroupBox("⚠️ Discard Status")
        discard_layout = QGridLayout()
        
        self.discard_status = QLabel("--")
        self.discard_criteria_years = QLabel("--")
        self.discard_criteria_kms = QLabel("--")
        self.discard_remaining = QLabel("--")
        
        discard_layout.addWidget(QLabel("Status:"), 0, 0)
        discard_layout.addWidget(self.discard_status, 0, 1)
        discard_layout.addWidget(QLabel("Criteria (Years):"), 0, 2)
        discard_layout.addWidget(self.discard_criteria_years, 0, 3)
        
        discard_layout.addWidget(QLabel("Criteria (KMs):"), 1, 0)
        discard_layout.addWidget(self.discard_criteria_kms, 1, 1)
        discard_layout.addWidget(QLabel("Remaining Life:"), 1, 2)
        discard_layout.addWidget(self.discard_remaining, 1, 3)
        
        discard_group.setLayout(discard_layout)
        return discard_group

    def create_demand_section(self):
        """Create and return the demand forecast section."""
        demand_group = QGroupBox("📈 All Equipment Demands")
        demand_layout = QVBoxLayout()
        
        # Demand table with more comprehensive columns
        self.demand_table = ReadOnlyTableWidget()
        self.demand_table.setMaximumHeight(200)  # Increased height for more demand types
        demand_layout.addWidget(self.demand_table)
        
        demand_group.setLayout(demand_layout)
        return demand_group

    def create_mr_section(self):
        """Create and return the Medium Reset section."""
        mr_group = QGroupBox("🔄 Medium Reset Records")
        mr_layout = QVBoxLayout(mr_group)
        
        # Header
        header_layout = QHBoxLayout()
        header_label = QLabel("🔄 Medium Reset Records")
        header_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #2196F3;")
        header_layout.addWidget(header_label)
        header_layout.addStretch()
        
        # Refresh button
        refresh_btn = QPushButton("🔄 Refresh")
        refresh_btn.clicked.connect(lambda: self.load_mr_data(self.current_equipment_id))
        header_layout.addWidget(refresh_btn)
        
        mr_layout.addLayout(header_layout)
        
        # Create table for MR records
        from ui.custom_widgets import ReadOnlyTableWidget
        self.mr_table = ReadOnlyTableWidget()
        mr_layout.addWidget(self.mr_table)
        
        # Load MR data
        self.load_mr_data(self.current_equipment_id)
        
        return mr_group
    
    def create_overhaul_section(self):
        """Create and return the Overhaul section."""
        overhaul_group = QGroupBox("🔧 Overhaul Records")
        overhaul_layout = QVBoxLayout(overhaul_group)
        
        # Header
        header_layout = QHBoxLayout()
        header_label = QLabel("🔧 Overhaul Records")
        header_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #FF9800;")
        header_layout.addWidget(header_label)
        header_layout.addStretch()
        
        # Refresh button
        refresh_btn = QPushButton("🔄 Refresh")
        refresh_btn.clicked.connect(lambda: self.load_overhaul_data(self.current_equipment_id))
        header_layout.addWidget(refresh_btn)
        
        overhaul_layout.addLayout(header_layout)
        
        # Create table for Overhaul records
        from ui.custom_widgets import ReadOnlyTableWidget
        self.overhaul_table = ReadOnlyTableWidget()
        overhaul_layout.addWidget(self.overhaul_table)
        
        # Load Overhaul data
        self.load_overhaul_data(self.current_equipment_id)
        
        return overhaul_group
    
    def load_equipment_data(self, equipment_id):
        """Load all data for the specified equipment."""
        if not equipment_id:
            self.clear_data()
            return
            
        self.current_equipment_id = equipment_id
        
        try:
            # Load equipment details
            self.load_equipment_summary()
            self.load_statistics()
            self.load_maintenance_data()
            self.load_fluids_data()
            self.load_discard_data()
            self.load_demand_data()
            self.load_mr_data(equipment_id)
            self.load_overhaul_data(equipment_id)
        except Exception as e:
            logger.error(f"Error loading equipment life data: {e}")
            
    def load_equipment_summary(self):
        """Load equipment summary information."""
        if not self.current_equipment_id:
            return
            
        equipment = Equipment.get_by_id(self.current_equipment_id)
        if equipment:
            self.summary_ba_number.setText(equipment.ba_number or "Not Assigned")
            self.summary_make_type.setText(equipment.make_and_type or "")
            self.summary_vintage.setText(f"{equipment.vintage_years:.2f} years")
            self.summary_meterage.setText(f"{equipment.meterage_kms:.2f} km")
            self.summary_hours.setText(f"{equipment.hours_run_total:.2f} hrs")
            self.summary_status.setText("Active" if equipment.is_active else "Inactive")
            
    def load_statistics(self):
        """Load and calculate usage statistics."""
        if not self.current_equipment_id:
            return
            
        equipment = Equipment.get_by_id(self.current_equipment_id)
        if equipment:
            # Calculate monthly averages (assuming vintage is in years)
            months = max(equipment.vintage_years * 12, 1)
            avg_monthly_km = equipment.meterage_kms / months
            avg_monthly_hrs = equipment.hours_run_total / months
            
            self.stats_monthly_km.setText(f"{avg_monthly_km:.2f} km")
            self.stats_monthly_hrs.setText(f"{avg_monthly_hrs:.2f} hrs")
            
            # Calculate efficiency (km per hour)
            if equipment.hours_run_total > 0:
                efficiency = equipment.meterage_kms / equipment.hours_run_total
                self.stats_efficiency.setText(f"{efficiency:.2f} km/hr")
            else:
                self.stats_efficiency.setText("N/A")
                
            # Utilization (hours per day assuming equipment age)
            days = max(equipment.vintage_years * 365, 1)
            utilization = equipment.hours_run_total / days
            self.stats_utilization.setText(f"{utilization:.2f} hrs/day")
            
    def load_maintenance_data(self):
        """Load maintenance history data with proper status calculation."""
        if not self.current_equipment_id:
            return
            
        try:
            maintenance_records = Maintenance.get_by_equipment(self.current_equipment_id)
            
            # Handle null returns - ensure we have a list
            if maintenance_records is None:
                maintenance_records = []
            
            # Count statuses using proper status calculation
            completed = 0
            pending = 0
            overdue = 0
            critical = 0
            warning = 0
            
            from datetime import date
            today = date.today()
            
            for record in maintenance_records:
                status = record.get('status', '')
                due_date = record.get('due_date')
                
                if status == 'completed':
                    completed += 1
                elif status == 'archived':
                    # Don't count archived records
                    continue
                else:
                    # Calculate status based on due date
                    if due_date:
                        calculated_status = utils.get_maintenance_status(due_date)
                        if calculated_status == "overdue":
                            overdue += 1
                        elif calculated_status == "critical":
                            critical += 1
                        elif calculated_status == "warning":
                            warning += 1
                        else:
                            pending += 1
                    else:
                        pending += 1
            
            # Update status labels with color coding
            self.maintenance_completed.setText(f"Completed: {completed}")
            self.maintenance_completed.setStyleSheet("color: #4CAF50; font-weight: bold;")
            
            self.maintenance_pending.setText(f"Pending: {pending}")
            self.maintenance_pending.setStyleSheet("color: #2196F3; font-weight: bold;")
            
            overdue_text = f"Overdue: {overdue}"
            if critical > 0:
                overdue_text += f" | Critical: {critical}"
            if warning > 0:
                overdue_text += f" | Warning: {warning}"
            
            self.maintenance_overdue.setText(overdue_text)
            if overdue > 0 or critical > 0:
                self.maintenance_overdue.setStyleSheet("color: #F44336; font-weight: bold;")
            elif warning > 0:
                self.maintenance_overdue.setStyleSheet("color: #FF9800; font-weight: bold;")
            else:
                self.maintenance_overdue.setStyleSheet("color: #4CAF50; font-weight: bold;")
            
            # Load recent maintenance into table with enhanced data
            headers = ["Type", "Category", "Due Date", "Status", "Notes"]
            table_data = []
            
            # Get last 10 maintenance records (increased from 5)
            recent_records = maintenance_records[-10:] if maintenance_records else []
            for record in recent_records:
                # Calculate proper status
                status = record.get('status', '')
                due_date = record.get('due_date')
                
                if status == 'completed':
                    display_status = "✅ Completed"
                    status_color = "#4CAF50"
                elif status == 'archived':
                    display_status = "📁 Archived"
                    status_color = "#9E9E9E"
                else:
                    if due_date:
                        calculated_status = utils.get_maintenance_status(due_date)
                        if calculated_status == "overdue":
                            display_status = "🔴 Overdue"
                            status_color = "#F44336"
                        elif calculated_status == "critical":
                            display_status = "🔴 Critical"
                            status_color = "#FF5722"
                        elif calculated_status == "warning":
                            display_status = "🟡 Warning"
                            status_color = "#FF9800"
                        else:
                            display_status = "🟢 Pending"
                            status_color = "#4CAF50"
                    else:
                        display_status = "❓ Unknown"
                        status_color = "#9E9E9E"
                
                notes = record.get('completion_notes', '') or ''
                if len(notes) > 40:
                    notes = notes[:40] + "..."
                
                # Format due date for display
                due_date_display = ""
                if due_date:
                    due_date_display = utils.format_date_for_display(due_date)
                    
                table_data.append({
                    "Type": record.get('maintenance_type', ''),
                    "Category": record.get('maintenance_category', ''),
                    "Due Date": due_date_display,
                    "Status": display_status,
                    "Notes": notes
                })
                
            self.maintenance_table.set_data(headers, table_data)
            
        except Exception as e:
            logger.error(f"Error loading maintenance data: {e}")
            # Show error in UI
            self.maintenance_completed.setText("Error loading data")
            self.maintenance_pending.setText("")
            self.maintenance_overdue.setText("")
            
    def load_fluids_data(self):
        """Load fluids data."""
        if not self.current_equipment_id:
            return
            
        try:
            fluids = Fluid.get_by_equipment(self.current_equipment_id)
            
            # Handle null returns
            if fluids is None:
                fluids = []
            
            headers = ["Fluid Type", "Capacity", "Grade", "Unit"]
            table_data = []
            
            for fluid in fluids:
                table_data.append({
                    "Fluid Type": fluid.get('fluid_type', ''),
                    "Capacity": f"{fluid.get('capacity_ltrs_kg', 0):.2f}",
                    "Grade": fluid.get('grade', ''),
                    "Unit": fluid.get('accounting_unit', 'Ltr')
                })
                
            self.fluids_table.set_data(headers, table_data)
            
        except Exception as e:
            logger.error(f"Error loading fluids data: {e}")
            
    def load_discard_data(self):
        """Load discard criteria data."""
        if not self.current_equipment_id:
            return
            
        try:
            discard_criteria_list = DiscardCriteria.get_by_equipment(self.current_equipment_id)
            equipment = Equipment.get_by_id(self.current_equipment_id)
            
            # Handle the case where get_by_equipment returns a list instead of single record
            if discard_criteria_list and isinstance(discard_criteria_list, list):
                # Take the first record if it's a list
                discard_criteria = discard_criteria_list[0] if discard_criteria_list else None
            else:
                discard_criteria = discard_criteria_list
            
            if discard_criteria and equipment:
                criteria_years = discard_criteria.get('criteria_years', 0)
                criteria_kms = discard_criteria.get('criteria_kms', 0)
                
                self.discard_criteria_years.setText(f"{criteria_years} years")
                self.discard_criteria_kms.setText(f"{criteria_kms:,.0f} km")
                
                # Calculate remaining life
                remaining_years = max(0, criteria_years - equipment.vintage_years)
                remaining_kms = max(0, criteria_kms - equipment.meterage_kms)
                
                if remaining_years <= 0 or remaining_kms <= 0:
                    self.discard_status.setText("⚠️ Due for Discard")
                    self.discard_status.setStyleSheet("color: red; font-weight: bold;")
                    self.discard_remaining.setText("Exceeded criteria")
                else:
                    self.discard_status.setText("✅ Normal")
                    self.discard_status.setStyleSheet("color: green; font-weight: bold;")
                    self.discard_remaining.setText(f"{remaining_years:.1f} yrs / {remaining_kms:,.0f} km")
            else:
                self.discard_status.setText("No criteria set")
                self.discard_criteria_years.setText("--")
                self.discard_criteria_kms.setText("--")
                self.discard_remaining.setText("--")
                
        except Exception as e:
            logger.error(f"Error loading discard data: {e}")
            
    def load_demand_data(self):
        """Load all types of demand forecast data for this equipment."""
        if not self.current_equipment_id:
            return
            
        try:
            from datetime import datetime
            current_year = datetime.now().year
            
            headers = ["Demand Type", "Item", "Requirement", "Unit", "Source", "Due Date"]
            table_data = []
            
            # 1. Fluid demands from demand_forecast table
            try:
                fluid_query = """
                    SELECT df.*, f.fluid_type, f.accounting_unit 
                    FROM demand_forecast df
                    JOIN fluids f ON df.fluid_id = f.fluid_id
                    WHERE f.equipment_id = %s AND df.fiscal_year = %s
                """
                fluid_records = database.execute_query(fluid_query, (self.current_equipment_id, str(current_year)))
                
                for record in fluid_records or []:
                    table_data.append({
                        "Demand Type": "🛢️ Fluid",
                        "Item": record.get('fluid_type', ''),
                        "Requirement": f"{record.get('total_requirement', 0):.2f}",
                        "Unit": record.get('accounting_unit', 'Ltr'),
                        "Source": "Maintenance Schedule",
                        "Due Date": f"FY {record.get('fiscal_year', '')}"
                    })
            except Exception as e:
                logger.warning(f"Error loading fluid demands: {e}")
            
            # 2. Tyre demands from tyre_maintenance table
            try:
                tyre_records = TyreMaintenance.get_by_equipment(self.current_equipment_id)
                if tyre_records:
                    # If it's a single record (not a list), convert to list
                    if not isinstance(tyre_records, list):
                        tyre_records = [tyre_records]
                    
                    for record in tyre_records:
                        # Calculate tyre replacement need
                        current_kms = record.get('meterage_kms', 0)
                        condition_kms = record.get('tyre_condition_kms', 0)
                        
                        if condition_kms > 0 and current_kms >= condition_kms * 0.8:  # 80% of condition limit
                            urgency = "🔴 Soon" if current_kms >= condition_kms else "🟡 Monitor"
                            table_data.append({
                                "Demand Type": "🚗 Tyre",
                                "Item": "Tyre Replacement",
                                "Requirement": str(record.get('quantity', 4)),
                                "Unit": "Set",
                                "Source": "Tyre Condition",
                                "Due Date": urgency
                            })
                        
                        # Tyre rotation demand
                        rotation_kms = record.get('tyre_rotation_kms', 0)
                        if rotation_kms > 0:
                            table_data.append({
                                "Demand Type": "🔄 Tyre Service",
                                "Item": "Tyre Rotation",
                                "Requirement": "1",
                                "Unit": "Service",
                                "Source": "Maintenance Schedule",
                                "Due Date": f"Every {rotation_kms:,} km"
                            })
            except Exception as e:
                logger.warning(f"Error loading tyre demands: {e}")
            
            # 3. Battery demands (estimated from equipment age and type)
            try:
                equipment = Equipment.get_by_id(self.current_equipment_id)
                if equipment:
                    vintage_years = equipment.vintage_years
                    equipment_type = equipment.make_and_type or ""
                    
                    # Estimate battery replacement need based on age and type
                    if vintage_years > 3:  # Batteries typically last 3-5 years
                        battery_count = 1
                        if any(keyword in equipment_type.lower() for keyword in ['truck', 'heavy', 'crane']):
                            battery_count = 2
                        
                        urgency = "🔴 Overdue" if vintage_years > 5 else "🟡 Due Soon"
                        table_data.append({
                            "Demand Type": "🔋 Battery",
                            "Item": "Battery Replacement",
                            "Requirement": str(battery_count),
                            "Unit": "Nos",
                            "Source": "Age-based Estimate",
                            "Due Date": urgency
                        })
            except Exception as e:
                logger.warning(f"Error estimating battery demands: {e}")
            
            # 4. Overhaul demands from overhauls table
            try:
                from models import Overhaul
                overhaul_records = Overhaul.get_by_equipment(self.current_equipment_id)
                
                for record in overhaul_records or []:
                    if record.get('status') != 'completed':
                        due_date = record.get('due_date', '')
                        if due_date:
                            due_date_display = utils.format_date_for_display(due_date)
                        else:
                            due_date_display = "Pending"
                            
                        table_data.append({
                            "Demand Type": "🔧 Overhaul",
                            "Item": record.get('overhaul_type', 'General Overhaul'),
                            "Requirement": "1",
                            "Unit": "Service",
                            "Source": "Overhaul Schedule",
                            "Due Date": due_date_display
                        })
            except Exception as e:
                logger.warning(f"Error loading overhaul demands: {e}")
            
            # 5. Medium Reset demands from medium_reset table
            try:
                mr_query = """
                    SELECT * FROM medium_resets 
                    WHERE equipment_id = %s AND status != 'completed'
                """
                mr_records = database.execute_query(mr_query, (self.current_equipment_id,))
                
                for record in mr_records or []:
                    due_date = record.get('due_date', '')
                    if due_date:
                        due_date_display = utils.format_date_for_display(due_date)
                    else:
                        due_date_display = "Pending"
                        
                    table_data.append({
                        "Demand Type": "🔄 Medium Reset",
                        "Item": "Medium Reset Service",
                        "Requirement": "1",
                        "Unit": "Service",
                        "Source": "MR Schedule",
                        "Due Date": due_date_display
                    })
            except Exception as e:
                logger.warning(f"Error loading medium reset demands: {e}")
            
            # Sort by demand type and urgency
            def sort_key(item):
                urgency_order = {"🔴": 0, "🟡": 1, "🟢": 2}
                due_date = item.get("Due Date", "")
                urgency = 2  # Default
                for color, priority in urgency_order.items():
                    if color in due_date:
                        urgency = priority
                        break
                return (urgency, item.get("Demand Type", ""))
            
            table_data.sort(key=sort_key)
            
            # Set table data
            self.demand_table.set_data(headers, table_data)
            
            if not table_data:
                # Show placeholder if no demands found
                placeholder_data = [{
                    "Demand Type": "ℹ️ Info",
                    "Item": "No active demands",
                    "Requirement": "--",
                    "Unit": "--",
                    "Source": "System",
                    "Due Date": "All up to date"
                }]
                self.demand_table.set_data(headers, placeholder_data)
            
        except Exception as e:
            logger.error(f"Error loading demand data: {e}")
            # Show error in table
            error_data = [{
                "Demand Type": "❌ Error",
                "Item": "Failed to load demands",
                "Requirement": "--",
                "Unit": "--",
                "Source": "System",
                "Due Date": "Check logs"
            }]
            self.demand_table.set_data(headers, error_data)
    
    def load_mr_data(self, equipment_id):
        """Load Medium Reset data for the equipment."""
        try:
            # Query MR records for this equipment
            mr_query = """
                SELECT * FROM medium_resets 
                WHERE equipment_id = %s 
                ORDER BY done_date DESC, due_date DESC
            """
            mr_records = database.execute_query(mr_query, (equipment_id,))
            
            # Set up table headers
            headers = ["ID", "Type", "Done Date", "Due Date", "Status", "Notes", "Meter Reading", "Hours Reading"]
            
            # Prepare data for table
            table_data = []
            for record in mr_records or []:
                done_date = utils.format_date_for_display(record.get('done_date')) if record.get('done_date') else ''
                due_date = utils.format_date_for_display(record.get('due_date')) if record.get('due_date') else ''
                
                # Format status with color indicators
                status = record.get('status', 'unknown').lower()
                if status == 'completed':
                    status_display = "✅ Completed"
                elif status == 'pending':
                    status_display = "🟡 Pending"
                elif status == 'scheduled':
                    status_display = "📅 Scheduled"
                else:
                    status_display = f"❓ {status.title()}"
                
                # Truncate notes if too long
                notes = record.get('notes', '') or ''
                if len(notes) > 50:
                    notes = notes[:50] + "..."
                
                table_data.append({
                    "ID": record.get('medium_reset_id', ''),
                    "Type": record.get('reset_type', ''),
                    "Done Date": done_date,
                    "Due Date": due_date,
                    "Status": status_display,
                    "Notes": notes,
                    "Meter Reading": record.get('meter_reading', ''),
                    "Hours Reading": record.get('hours_reading', '')
                })
            
            # Set table data
            self.mr_table.set_data(headers, table_data, id_column=0)
            
        except Exception as e:
            logger.error(f"Error loading MR data: {e}")
            # Show error in table
            error_data = [{
                "ID": "Error",
                "Type": "Failed to load MR data",
                "Done Date": "",
                "Due Date": "",
                "Status": "❌ Error",
                "Notes": str(e)[:50],
                "Meter Reading": "",
                "Hours Reading": ""
            }]
            self.mr_table.set_data(headers, error_data)
    
    def load_overhaul_data(self, equipment_id):
        """Load Overhaul data for the equipment."""
        try:
            # Query Overhaul records for this equipment using the model
            from models import Overhaul
            overhaul_records = Overhaul.get_by_equipment(equipment_id)
            
            # Set up table headers
            headers = ["ID", "Type", "Done Date", "Due Date", "Status", "Description", "Meter Reading", "Hours Reading"]
            
            # Prepare data for table
            table_data = []
            for record in overhaul_records or []:
                done_date = utils.format_date_for_display(record.get('done_date')) if record.get('done_date') else ''
                due_date = utils.format_date_for_display(record.get('due_date')) if record.get('due_date') else ''
                
                # Format status with color indicators
                status = record.get('status', 'unknown').lower()
                if status == 'completed':
                    status_display = "✅ Completed"
                elif status == 'pending':
                    status_display = "🟡 Pending"
                elif status == 'scheduled':
                    status_display = "📅 Scheduled"
                else:
                    status_display = f"❓ {status.title()}"
                
                # Truncate description if too long
                description = record.get('description', '') or ''
                if len(description) > 50:
                    description = description[:50] + "..."
                
                table_data.append({
                    "ID": record.get('overhaul_id', ''),
                    "Type": record.get('overhaul_type', ''),
                    "Done Date": done_date,
                    "Due Date": due_date,
                    "Status": status_display,
                    "Description": description,
                    "Meter Reading": record.get('meter_reading', ''),
                    "Hours Reading": record.get('hours_reading', '')
                })
            
            # Set table data
            self.overhaul_table.set_data(headers, table_data, id_column=0)
            
        except Exception as e:
            logger.error(f"Error loading Overhaul data: {e}")
            # Show error in table
            error_data = [{
                "ID": "Error",
                "Type": "Failed to load Overhaul data",
                "Done Date": "",
                "Due Date": "",
                "Status": "❌ Error",
                "Description": str(e)[:50],
                "Meter Reading": "",
                "Hours Reading": ""
            }]
            self.overhaul_table.set_data(headers, error_data)
    
    def open_in_maintenance_tab(self):
        """Open the maintenance tab for the selected equipment."""
        if not self.current_equipment_id:
            QMessageBox.warning(
                self,
                "No Selection",
                "Please select an equipment first.",
                QMessageBox.StandardButton.Ok
            )
            return
        
        try:
            # Get the main window (navigate up the widget hierarchy)
            main_window = self.window()
            while main_window and not hasattr(main_window, 'switch_to_maintenance_tab'):
                main_window = main_window.parent()
            
            if main_window and hasattr(main_window, 'switch_to_maintenance_tab'):
                # Show a simple selection for maintenance category
                from PyQt5.QtWidgets import QInputDialog
                
                categories = ['TM-1', 'TM-2', 'Yearly', 'Monthly']
                category, ok = QInputDialog.getItem(
                    self,
                    "Select Maintenance Category",
                    "Choose maintenance category to create:",
                    categories,
                    0, False
                )
                
                if ok and category:
                    main_window.switch_to_maintenance_tab(
                        equipment_id=self.current_equipment_id,
                        maintenance_category=category
                    )
            else:
                QMessageBox.information(
                    self,
                    "Info",
                    "Maintenance tab connection not available in this context.",
                    QMessageBox.StandardButton.Ok
                )
                    
        except Exception as e:
            logger.error(f"Error opening maintenance tab: {e}")
            QMessageBox.critical(
                self,
                "Error",
                f"Failed to open maintenance tab:\n{str(e)}",
                QMessageBox.StandardButton.Ok
            )


class EquipmentWidget(QWidget):
    """Widget for managing equipment."""
    
    def __init__(self, parent=None):
        """Initialize the Equipment widget."""
        super().__init__(parent)
        self._auto_calculate_enabled = True
        self._loading_data = False  # Flag to prevent triggering calculations during form population
        self.load_auto_calculate_setting()
        self.setup_ui()
        
    def load_auto_calculate_setting(self):
        """Load auto-calculate setting from database."""
        try:
            query = "SELECT value FROM settings WHERE key = 'auto_calculate_enabled'"
            result = database.execute_query(query, fetchall=False)
            
            if result:
                # Convert stored value ('0' or '1') to boolean
                self._auto_calculate_enabled = bool(int(result['value']))
            else:
                # Default to True if setting doesn't exist yet
                self._auto_calculate_enabled = True
                
            logging.info(f"Auto-calculate setting loaded: {self._auto_calculate_enabled}")
        except Exception as e:
            logging.error(f"Error loading auto-calculate setting: {e}")
            # Default to True if there's any error
            self._auto_calculate_enabled = True
        
    def setup_ui(self):
        """Set up the equipment widget UI."""
        # Create main layout
        main_layout = QHBoxLayout(self)
        
        # Create splitter for resizable sections
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # Create left panel (equipment list)
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        
        # Create search field
        search_layout = QHBoxLayout()
        search_label = QLabel("Search:")
        self.search_field = QLineEdit()
        self.search_field.setPlaceholderText("Search by BA number, serial number or type...")
        self.search_field.textChanged.connect(self.filter_equipment)
        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_field)
        left_layout.addLayout(search_layout)
        
        # Create table for equipment list
        from ui.custom_widgets import ReadOnlyTableWidget
        self.equipment_table = ReadOnlyTableWidget()
        self.equipment_table.row_clicked.connect(self.equipment_selected)
        left_layout.addWidget(self.equipment_table)
        
        # Create buttons for equipment actions
        button_layout = QHBoxLayout()
        self.add_button = QPushButton("Add New")
        self.edit_button = QPushButton("Edit")
        self.delete_button = QPushButton("Delete")
        self.delete_all_button = QPushButton("Delete All")
        self.send_to_maintenance_button = QPushButton("Send to Maintenance")
        
        self.edit_button.setEnabled(False)
        self.delete_button.setEnabled(False)
        self.send_to_maintenance_button.setEnabled(False)
        
        # Style the send to maintenance button
        self.send_to_maintenance_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        
        self.add_button.clicked.connect(self.add_equipment)
        self.edit_button.clicked.connect(self.edit_equipment)
        self.delete_button.clicked.connect(self.delete_equipment)
        self.delete_all_button.clicked.connect(self.delete_all_equipment)
        self.send_to_maintenance_button.clicked.connect(self.send_to_maintenance)
        
        button_layout.addWidget(self.add_button)
        button_layout.addWidget(self.edit_button)
        button_layout.addWidget(self.delete_button)
        button_layout.addWidget(self.delete_all_button)
        button_layout.addWidget(self.send_to_maintenance_button)
        left_layout.addLayout(button_layout)
        
        # Create right panel with tabs
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        
        # Create tab widget for right panel
        self.right_tab_widget = QTabWidget()
        
        # Create equipment details tab (existing form)
        self.equipment_details_widget = QWidget()
        details_layout = QVBoxLayout(self.equipment_details_widget)
        self.create_equipment_form(details_layout)
        
        # Create equipment life tab (new comprehensive view)
        self.equipment_life_widget = EquipmentLifeWidget(self)
        
        # Add tabs
        self.right_tab_widget.addTab(self.equipment_details_widget, "📝 Equipment Details")
        self.right_tab_widget.addTab(self.equipment_life_widget, "🔍 Equipment Life")
        
        # Connect tab change signal
        self.right_tab_widget.currentChanged.connect(self.right_tab_changed)
        
        # Add tab widget to right panel
        right_layout.addWidget(self.right_tab_widget)
        
        # Add panels to splitter
        splitter.addWidget(left_panel)
        splitter.addWidget(right_panel)
        
        # Set initial sizes
        splitter.setSizes([400, 600])
        
        # Add splitter to main layout
        main_layout.addWidget(splitter)

    def create_equipment_form(self, parent_layout):
        """Create the equipment details form."""
        # Create form group
        form_group = QGroupBox("Equipment Details")
        form_layout = QFormLayout()
        
        # Create form fields
        self.id_field = QLineEdit()
        self.id_field.setReadOnly(True)
        
        self.ba_number_field = QLineEdit()
        self.ba_number_field.setPlaceholderText("e.g., 86R3124A")
        
        self.serial_field = QLineEdit()
        self.make_type_field = QLineEdit()
        self.units_field = QSpinBox()
        self.units_field.setMinimum(0)
        self.units_field.setMaximum(1000)
        
        self.vintage_field = QDoubleSpinBox()
        self.vintage_field.setMinimum(0)
        self.vintage_field.setMaximum(100)
        self.vintage_field.setDecimals(2)
        self.vintage_field.setSuffix(" years")
        
        self.meterage_field = QDoubleSpinBox()
        self.meterage_field.setMinimum(0)
        self.meterage_field.setMaximum(1000000)
        self.meterage_field.setDecimals(2)
        self.meterage_field.setSuffix(" km")
        
        self.meterage_description_field = QLineEdit()
        self.meterage_description_field.setPlaceholderText("e.g., 5000 Hrs or 105000kms which ever is earlier")
        self.meterage_description_field.setReadOnly(True)  # Read-only by default
        
        self.prev_month_field = QDoubleSpinBox()
        self.prev_month_field.setMinimum(0)
        self.prev_month_field.setMaximum(100000)
        self.prev_month_field.setDecimals(2)
        self.prev_month_field.setSuffix(" km")
        
        self.curr_month_field = QDoubleSpinBox()
        self.curr_month_field.setMinimum(0)
        self.curr_month_field.setMaximum(100000)
        self.curr_month_field.setDecimals(2)
        self.curr_month_field.setSuffix(" km")
        
        # Hours tracking fields
        self.hours_total_field = QDoubleSpinBox()
        self.hours_total_field.setMinimum(0)
        self.hours_total_field.setMaximum(1000000)
        self.hours_total_field.setDecimals(2)
        self.hours_total_field.setSuffix(" hrs")
        
        self.hours_prev_month_field = QDoubleSpinBox()
        self.hours_prev_month_field.setMinimum(0)
        self.hours_prev_month_field.setMaximum(100000)
        self.hours_prev_month_field.setDecimals(2)
        self.hours_prev_month_field.setSuffix(" hrs")
        
        self.hours_curr_month_field = QDoubleSpinBox()
        self.hours_curr_month_field.setMinimum(0)
        self.hours_curr_month_field.setMaximum(100000)
        self.hours_curr_month_field.setDecimals(2)
        self.hours_curr_month_field.setSuffix(" hrs")
        
        self.active_field = QCheckBox("Active")
        self.active_field.setChecked(True)
        
        self.remarks_field = QTextEdit()
        self.remarks_field.setMaximumHeight(100)
        
        # Add basic fields to form
        form_layout.addRow("ID:", self.id_field)
        form_layout.addRow("BA Number:", self.ba_number_field)
        form_layout.addRow("Serial Number:", self.serial_field)
        form_layout.addRow("Make and Type:", self.make_type_field)
        form_layout.addRow("Units Held:", self.units_field)
        form_layout.addRow("Vintage (Years):", self.vintage_field)
        
        # Create kilometers tracking group
        km_group = QGroupBox("Kilometers Tracking")
        km_layout = QFormLayout()
        km_layout.addRow("Total Meterage (km):", self.meterage_field)
        km_layout.addRow("Meterage Description:", self.meterage_description_field)
        km_layout.addRow("Previous Month (km):", self.prev_month_field)
        km_layout.addRow("Current Month (km):", self.curr_month_field)
        # Connect current month km to auto-update total
        self.curr_month_field.valueChanged.connect(self.on_current_km_changed)
        km_group.setLayout(km_layout)
        form_layout.addRow("", km_group)
        
        # Create hours tracking group
        hours_group = QGroupBox("Hours Tracking")
        hours_layout = QFormLayout()
        hours_layout.addRow("Total Hours Run:", self.hours_total_field)
        hours_layout.addRow("Previous Month (hrs):", self.hours_prev_month_field)
        hours_layout.addRow("Current Month (hrs):", self.hours_curr_month_field)
        # Connect current month hours to auto-update total
        self.hours_curr_month_field.valueChanged.connect(self.on_current_hours_changed)
        hours_group.setLayout(hours_layout)
        form_layout.addRow("", hours_group)
        
        # Create auto-calculate toggle
        self.auto_calculate_field = QCheckBox("Auto-calculate totals from current month values")
        self.auto_calculate_field.setChecked(self._auto_calculate_enabled)
        self.auto_calculate_field.stateChanged.connect(self.toggle_auto_calculate)
        form_layout.addRow("", self.auto_calculate_field)
        
        form_layout.addRow("", self.active_field)
        form_layout.addRow("Remarks:", self.remarks_field)
        
        form_group.setLayout(form_layout)
        
        # Create buttons for form actions
        button_layout = QHBoxLayout()
        self.save_button = QPushButton("Save")
        self.cancel_button = QPushButton("Cancel")
        
        self.save_button.clicked.connect(self.save_equipment)
        self.cancel_button.clicked.connect(self.cancel_edit)
        
        button_layout.addWidget(self.save_button)
        button_layout.addWidget(self.cancel_button)
        
        # Add form and buttons to parent layout
        parent_layout.addWidget(form_group)
        parent_layout.addLayout(button_layout)
        
        # Initially disable all fields
        self.set_form_enabled(False)
    
    def load_data(self):
        """Load equipment data."""
        logger.info("Loading equipment data")
        
        try:
            # Get all equipment
            equipment_list = Equipment.get_all()
            logger.info(f"Retrieved {len(equipment_list) if equipment_list else 0} equipment records")
            
            # Set up table headers - removed Units and Status columns
            headers = ["ID", "BA Number", "Serial Number", "Make & Type", "Vintage (Yrs)", "Meterage (km)", "Hours Run"]
            
            # Prepare data for table
            data = []
            if equipment_list:
                # Use row index as Serial Number
                for idx, equipment in enumerate(equipment_list, start=1):
                    # Display meterage description if available, otherwise show numeric value
                    meterage_display = equipment.get('meterage_description', '')
                    if not meterage_display or meterage_display.strip() in ['', '0', '-', 'nan', 'NaN']:
                        meterage_display = f"{equipment['meterage_kms']:.2f}" if equipment['meterage_kms'] else "0.00"
                    
                    # Add row data - removed Units and Status columns
                    row_data = {
                        "ID": equipment['equipment_id'],
                        "BA Number": equipment['ba_number'] or "Not Assigned",
                        "Serial Number": idx,
                        "Make & Type": equipment['make_and_type'] or "",
                        "Vintage (Yrs)": f"{equipment['vintage_years']:.2f}" if equipment['vintage_years'] else "0.00",
                        "Meterage (km)": meterage_display,
                        "Hours Run": f"{equipment.get('hours_run_total', 0):.2f}" if equipment.get('hours_run_total') else "0.00"
                    }
                    data.append(row_data)
            
            # Set table data
            logger.info(f"Setting {len(data)} rows in equipment table")
            self.equipment_table.set_data(headers, data, id_column=0)
            
            # Hide the ID column
            self.equipment_table.setColumnHidden(0, True)
            
            logger.info("Equipment data loaded successfully")
        except Exception as e:
            logger.error(f"Error loading equipment data: {e}")
    
    def filter_equipment(self):
        """Filter equipment list based on search text."""
        search_text = self.search_field.text().lower()
        
        # Show all rows if search text is empty
        if not search_text:
            for row in range(self.equipment_table.rowCount()):
                self.equipment_table.setRowHidden(row, False)
            return
        
        # Hide rows that don't match search text
        for row in range(self.equipment_table.rowCount()):
            match_found = False
            
            # Check BA Number, Serial Number, Make & Type columns, and Hours Run
            # Updated column indices: BA Number=1, Serial Number=2, Make & Type=3, Hours Run=6
            for col in [1, 2, 3, 6]:  # BA Number, Serial Number, Make & Type, Hours Run
                item = self.equipment_table.item(row, col)
                if item and search_text in item.text().lower():
                    match_found = True
                    break
            
            self.equipment_table.setRowHidden(row, not match_found)
    
    def equipment_selected(self, row):
        """Handle equipment selection."""
        # Get selected equipment ID
        equipment_id = self.equipment_table.get_selected_id()
            
        if equipment_id:
            # Enable edit, delete, and send to maintenance buttons
            self.edit_button.setEnabled(True)
            self.delete_button.setEnabled(True)
            self.send_to_maintenance_button.setEnabled(True)
                
            # Load equipment details in form
            self.load_equipment_details(equipment_id)
            
            # Load equipment life data
            self.equipment_life_widget.load_equipment_data(equipment_id)
        else:
            # Disable edit, delete, and send to maintenance buttons
            self.edit_button.setEnabled(False)
            self.delete_button.setEnabled(False)
            self.send_to_maintenance_button.setEnabled(False)
                
            # Clear form and life data
            self.clear_form()
            self.equipment_life_widget.clear_data()
            
    def right_tab_changed(self, index):
        """Handle right tab change event."""
        # If switching to Equipment Life tab and there's a selected equipment
        if index == 1:  # Equipment Life tab
            equipment_id = self.equipment_table.get_selected_id()
            if equipment_id:
                # Refresh equipment life data
                self.equipment_life_widget.load_equipment_data(equipment_id)
    
    def load_equipment_details(self, equipment_id):
        """Load equipment details into the form."""
        try:
            # Get equipment directly from database to ensure all fields
            query = "SELECT * FROM equipment WHERE equipment_id = %s"
            result = database.execute_query(query, (equipment_id,), fetchall=False)
            
            if not result:
                logging.error(f"Equipment with ID {equipment_id} not found in database")
                return
                
            # Create Equipment object with all fields
            equipment = Equipment(
                equipment_id=result['equipment_id'],
                serial_number=result['serial_number'],
                make_and_type=result['make_and_type'],
                ba_number=result['ba_number'],
                units_held=result['units_held'],
                vintage_years=result['vintage_years'],
                meterage_kms=result['meterage_kms'],
                meterage_description=result.get('meterage_description'),
                km_hrs_run_previous_month=result['km_hrs_run_previous_month'],
                km_hrs_run_current_month=result['km_hrs_run_current_month'],
                hours_run_total=result.get('hours_run_total', 0),
                hours_run_previous_month=result.get('hours_run_previous_month', 0),
                hours_run_current_month=result.get('hours_run_current_month', 0),
                is_active=result['is_active'],
                remarks=result['remarks']
            )
            
            # Debug output to verify data
            logging.debug(f"Loading equipment: {equipment_id}, KM: {equipment.meterage_kms}, Hours: {equipment.hours_run_total}")
            
            # Set flag to prevent triggering calculations during form population
            self._loading_data = True
                
            # ID and Basic Info
            self.id_field.setText(str(equipment.equipment_id))
            self.ba_number_field.setText(equipment.ba_number or '')
            self.serial_field.setText(equipment.serial_number or '')
            self.make_type_field.setText(equipment.make_and_type or '')
            self.units_field.setValue(int(equipment.units_held) if equipment.units_held is not None else 1)
            self.vintage_field.setValue(int(equipment.vintage_years) if equipment.vintage_years is not None else 0)
            
            # Set kilometers tracking values - explicit int conversion for spinboxes
            try:
                km_total = float(equipment.meterage_kms) if equipment.meterage_kms is not None else 0.0
                km_prev = float(equipment.km_hrs_run_previous_month) if equipment.km_hrs_run_previous_month is not None else 0.0
                km_curr = float(equipment.km_hrs_run_current_month) if equipment.km_hrs_run_current_month is not None else 0.0
                
                self.meterage_field.setValue(int(km_total))
                self.meterage_description_field.setText(equipment.meterage_description or '')
                self.prev_month_field.setValue(int(km_prev))
                self.curr_month_field.setValue(int(km_curr))
            except Exception as e:
                logging.error(f"Error setting km values: {e}")
            
            # Set hours tracking values - explicit int conversion for spinboxes
            try:
                hrs_total = float(equipment.hours_run_total) if equipment.hours_run_total is not None else 0.0
                hrs_prev = float(equipment.hours_run_previous_month) if equipment.hours_run_previous_month is not None else 0.0
                hrs_curr = float(equipment.hours_run_current_month) if equipment.hours_run_current_month is not None else 0.0
                
                self.hours_total_field.setValue(int(hrs_total))
                self.hours_prev_month_field.setValue(int(hrs_prev))
                self.hours_curr_month_field.setValue(int(hrs_curr))
            except Exception as e:
                logging.error(f"Error setting hours values: {e}")
            
            # Other fields
            self.active_field.setChecked(bool(equipment.is_active))
            self.remarks_field.setPlainText(equipment.remarks or '')
            
            # Reset loading flag
            self._loading_data = False
            
            # Set form fields to read-only
            self.set_form_enabled(False)
        except Exception as e:
            logging.error(f"Error loading equipment details: {e}")
            QMessageBox.critical(self, "Error", f"Failed to load equipment details: {str(e)}")
            
    def set_form_enabled(self, enabled):
        """Enable or disable form fields based on editing mode."""
        # BA and identification fields
        self.ba_number_field.setReadOnly(not enabled)
        self.serial_field.setReadOnly(not enabled)
        self.make_type_field.setReadOnly(not enabled)
        self.units_field.setReadOnly(not enabled)
        self.vintage_field.setReadOnly(not enabled)
        
        # Kilometers tracking fields
        self.meterage_field.setReadOnly(not enabled)
        self.meterage_description_field.setReadOnly(not enabled)
        self.prev_month_field.setReadOnly(not enabled)
        self.curr_month_field.setReadOnly(not enabled)
        
        # Hours tracking fields
        self.hours_total_field.setReadOnly(not enabled)
        self.hours_prev_month_field.setReadOnly(not enabled)
        self.hours_curr_month_field.setReadOnly(not enabled)
        
        # Status and remarks fields
        self.active_field.setEnabled(enabled)
        self.remarks_field.setReadOnly(not enabled)
        
        # Form action buttons
        self.save_button.setEnabled(enabled)
        self.cancel_button.setEnabled(enabled)
    
    def clear_form(self):
        """Clear the equipment form."""
        self.id_field.clear()
        self.ba_number_field.clear()
        self.serial_field.clear()
        self.make_type_field.clear()
        self.units_field.setValue(1)
        self.vintage_field.setValue(0)
        
        # Clear kilometers tracking fields
        self.meterage_field.setValue(0)
        self.meterage_description_field.clear()
        self.prev_month_field.setValue(0)
        self.curr_month_field.setValue(0)
        
        # Clear hours tracking fields
        self.hours_total_field.setValue(0)
        self.hours_prev_month_field.setValue(0)
        self.hours_curr_month_field.setValue(0)
        
        # Set default status and remarks
        self.active_field.setChecked(True)
        self.remarks_field.clear()
    
    def add_equipment(self):
        """Add new equipment."""
        # Clear form
        self.clear_form()
        
        # Enable form fields
        self.set_form_enabled(True)
        
        # Focus on first field
        self.ba_number_field.setFocus()
        
    def save_equipment(self):
        """Save equipment data from the form."""
        try:
            # Get form values
            equipment_id = self.id_field.text() or None
            ba_number = self.ba_number_field.text()
            serial_number = self.serial_field.text()
            make_and_type = self.make_type_field.text()
            units_held = self.units_field.value()
            vintage_years = self.vintage_field.value()
            
            # Kilometers tracking fields
            meterage_kms = self.meterage_field.value()
            meterage_description = self.meterage_description_field.text()
            km_hrs_run_previous_month = self.prev_month_field.value()
            km_hrs_run_current_month = self.curr_month_field.value()
            
            # Hours tracking fields
            hours_run_total = self.hours_total_field.value()
            hours_run_previous_month = self.hours_prev_month_field.value()
            hours_run_current_month = self.hours_curr_month_field.value()
            
            # Other fields
            is_active = self.active_field.isChecked()
            remarks = self.remarks_field.toPlainText()
            
            # Validate required fields
            if not serial_number:
                QMessageBox.warning(self, "Validation Error", "Serial Number is required.")
                return
            if not make_and_type:
                QMessageBox.warning(self, "Validation Error", "Make & Type is required.")
                return
                
            # Apply auto-calculation if enabled
            # This ensures the totals are consistent with monthly values
            if equipment_id and self._auto_calculate_enabled:
                try:
                    # Get original values from database
                    query = """SELECT meterage_kms, hours_run_total, 
                               km_hrs_run_current_month, hours_run_current_month 
                               FROM equipment WHERE equipment_id = %s"""
                    result = database.execute_query(query, (equipment_id,), fetchall=False)
                    
                    if result:
                        # Calculate km difference and update total
                        old_km_current = float(result.get('km_hrs_run_current_month') or 0)
                        old_meterage = float(result.get('meterage_kms') or 0)
                        km_diff = km_hrs_run_current_month - old_km_current
                        if abs(km_diff) > 0.001:  # Small epsilon for float comparison
                            meterage_kms = max(0, old_meterage + km_diff)
                            logging.info(f"Adjusted meterage total on save: {old_meterage:.2f} → {meterage_kms:.2f} (diff: {km_diff:+.2f})")
                        
                        # Calculate hours difference and update total
                        old_hrs_current = float(result.get('hours_run_current_month') or 0)
                        old_hours_total = float(result.get('hours_run_total') or 0)
                        hrs_diff = hours_run_current_month - old_hrs_current
                        if abs(hrs_diff) > 0.001:  # Small epsilon for float comparison
                            hours_run_total = max(0, old_hours_total + hrs_diff)
                            logging.info(f"Adjusted hours total on save: {old_hours_total:.2f} → {hours_run_total:.2f} (diff: {hrs_diff:+.2f})")
                except Exception as calc_err:
                    logging.error(f"Error in auto-calculation during save: {calc_err}")
            
            # Data validation - ensure non-negative values
            meterage_kms = max(0, meterage_kms)
            hours_run_total = max(0, hours_run_total)
            km_hrs_run_previous_month = max(0, km_hrs_run_previous_month)
            km_hrs_run_current_month = max(0, km_hrs_run_current_month)
            hours_run_previous_month = max(0, hours_run_previous_month)
            hours_run_current_month = max(0, hours_run_current_month)
            
            # Create equipment object
            equipment = Equipment(
                equipment_id=equipment_id,
                serial_number=serial_number,
                make_and_type=make_and_type,
                ba_number=ba_number,
                units_held=units_held,
                vintage_years=vintage_years,
                meterage_kms=meterage_kms,
                meterage_description=meterage_description,
                km_hrs_run_previous_month=km_hrs_run_previous_month,
                km_hrs_run_current_month=km_hrs_run_current_month,
                hours_run_total=hours_run_total,
                hours_run_previous_month=hours_run_previous_month,
                hours_run_current_month=hours_run_current_month,
                is_active=is_active,
                remarks=remarks
            )
            
            # Save equipment to database
            equipment_id = equipment.save()
            
            if equipment_id:
                QMessageBox.information(self, "Success", "Equipment saved successfully.")
                # Reload data, clear form and disable editing
                self.load_data()
                self.clear_form()
                self.set_form_enabled(False)
            else:
                QMessageBox.warning(self, "Error", "Failed to save equipment.")
        
        except Exception as e:
            QMessageBox.critical(self, "Error", f"An error occurred: {str(e)}")
            logging.error(f"Error saving equipment: {e}")
        
    
    def edit_equipment(self):
        """Edit selected equipment."""
        # Enable form fields
        self.set_form_enabled(True)
        
        # Focus on first editable field
        self.ba_number_field.setFocus()
        
    def cancel_edit(self):
        """Cancel editing equipment details."""
        # If there is a selected equipment, reload its details
        if self.id_field.text():
            self.load_equipment_details(int(self.id_field.text()))
        else:
            # Otherwise clear the form
            self.clear_form()
            
        # Disable form fields
        self.set_form_enabled(False)
        
    def on_current_km_changed(self, new_value):
        """Automatically update total kilometers when current month value changes."""
        # Skip calculation during form loading or when auto-calculate is disabled
        if not self._auto_calculate_enabled or self._loading_data:
            return
            
        # For new equipment, just update the total directly
        if not self.id_field.text():
            # Only update if the user is actively editing this field (not just initializing)
            if self.sender() == self.curr_month_field:
                # For new equipment: total should equal current month value
                prev_month = self.prev_month_field.value()
                self.meterage_field.setValue(prev_month + new_value)
            return
            
        try:
            equipment_id = int(self.id_field.text())
            
            # Get the original total and previous current month value
            query = "SELECT meterage_kms, km_hrs_run_current_month FROM equipment WHERE equipment_id = %s"
            result = database.execute_query(query, (equipment_id,), fetchall=False)
            
            if not result:
                return  # Equipment not found
                
            # Extract values with proper handling of None/NULL
            old_total = result.get('meterage_kms', 0)
            old_total = float(old_total if old_total is not None else 0)
            
            old_current = result.get('km_hrs_run_current_month', 0)
            old_current = float(old_current if old_current is not None else 0)
            
            # Calculate the difference
            diff = new_value - old_current
            
            # Only update if there's an actual difference
            if abs(diff) > 0.001:  # Use small epsilon for float comparison
                new_total = old_total + diff
                
                # Ensure total is never negative
                new_total = max(0, new_total)
                
                # Update the field
                self.meterage_field.blockSignals(True)
                self.meterage_field.setValue(new_total)
                self.meterage_field.blockSignals(False)
                
                # Log the update
                sign = '+' if diff > 0 else ''
                logging.info(f"Auto-updated total meterage for equipment ID {equipment_id}: {old_total:.2f} → {new_total:.2f} ({sign}{diff:.2f} km)")
        except Exception as e:
            logging.error(f"Error during auto-calculation of kilometers: {e}")
    
    def on_current_hours_changed(self, new_value):
        """Automatically update total hours when current month value changes."""
        # Skip calculation during form loading or when auto-calculate is disabled
        if not self._auto_calculate_enabled or self._loading_data:
            return
            
        # For new equipment, just update the total directly
        if not self.id_field.text():
            # Only update if the user is actively editing this field (not just initializing)
            if self.sender() == self.hours_curr_month_field:
                # For new equipment: total should equal previous month + current month
                prev_month = self.hours_prev_month_field.value()
                self.hours_total_field.setValue(prev_month + new_value)
            return
            
        try:
            equipment_id = int(self.id_field.text())
            
            # Get the original total and previous current month value
            query = "SELECT hours_run_total, hours_run_current_month FROM equipment WHERE equipment_id = %s"
            result = database.execute_query(query, (equipment_id,), fetchall=False)
            
            if not result:
                return  # Equipment not found
                
            # Extract values with proper handling of None/NULL
            old_total = result.get('hours_run_total', 0)
            old_total = float(old_total if old_total is not None else 0)
            
            old_current = result.get('hours_run_current_month', 0)
            old_current = float(old_current if old_current is not None else 0)
            
            # Calculate the difference
            diff = new_value - old_current
            
            # Only update if there's an actual difference
            if abs(diff) > 0.001:  # Use small epsilon for float comparison
                new_total = old_total + diff
                
                # Ensure total is never negative
                new_total = max(0, new_total)
                
                # Update the field
                self.hours_total_field.blockSignals(True)
                self.hours_total_field.setValue(new_total)
                self.hours_total_field.blockSignals(False)
                
                # Log the update
                sign = '+' if diff > 0 else ''
                logging.info(f"Auto-updated total hours for equipment ID {equipment_id}: {old_total:.2f} → {new_total:.2f} ({sign}{diff:.2f} hrs)")
        except Exception as e:
            logging.error(f"Error during auto-calculation of hours: {e}")
                
    def toggle_auto_calculate(self, state):
        """Toggle the auto-calculation of total from current month values."""
        self._auto_calculate_enabled = (state == Qt.Checked)
        logging.info(f"Auto-calculation of totals {'enabled' if self._auto_calculate_enabled else 'disabled'}")
        
        # If enabled, immediately recalculate totals based on current values
        if self._auto_calculate_enabled and self.id_field.text():
            self.recalculate_totals()
        
        # Save the setting to the database
        try:
            query = "SELECT value FROM settings WHERE key = 'auto_calculate_enabled'"
            result = database.execute_query(query, fetchall=False)
            
            if result:
                # Update existing setting
                query = "UPDATE settings SET value = ? WHERE key = 'auto_calculate_enabled'"
                database.execute_query(query, (str(int(self._auto_calculate_enabled)),), fetchall=False)
            else:
                # Create new setting
                query = "INSERT INTO settings (key, value, description) VALUES (?, ?, ?)"
                params = ('auto_calculate_enabled', str(int(self._auto_calculate_enabled)), 
                         'Automatically calculate totals from current month values')
                database.execute_query(query, params, fetchall=False)
        except Exception as e:
            logging.error(f"Error saving auto-calculate setting: {e}")
            # Don't fail if we can't save the setting
            
    def recalculate_totals(self):
        """Recalculate both km and hours totals based on current month values.
        This ensures the totals are consistent with the monthly data.
        """
        if not self._auto_calculate_enabled or not self.id_field.text():
            return
            
        try:
            equipment_id = int(self.id_field.text())
            
            # Get database values for comparison
            query = """SELECT meterage_kms, hours_run_total, 
                      km_hrs_run_current_month, hours_run_current_month, 
                      km_hrs_run_previous_month, hours_run_previous_month
                      FROM equipment WHERE equipment_id = %s"""
            result = database.execute_query(query, (equipment_id,), fetchall=False)
            
            if not result:
                return
                
            # Get current form values
            current_km = self.curr_month_field.value()
            current_hrs = self.hours_curr_month_field.value()
            prev_km = self.prev_month_field.value()
            prev_hrs = self.hours_prev_month_field.value()
            
            # Current correct totals should be:
            # Total = previous month + current month
            new_km_total = prev_km + current_km
            new_hrs_total = prev_hrs + current_hrs
            
            # Update UI fields with calculated values
            self.meterage_field.blockSignals(True)
            self.meterage_field.setValue(new_km_total)
            self.meterage_field.blockSignals(False)
            
            self.hours_total_field.blockSignals(True)
            self.hours_total_field.setValue(new_hrs_total)
            self.hours_total_field.blockSignals(False)
            
            logging.info(f"Recalculated totals for equipment ID {equipment_id}:")
            logging.info(f"Kilometers: {prev_km} (prev) + {current_km} (curr) = {new_km_total} (total)")
            logging.info(f"Hours: {prev_hrs} (prev) + {current_hrs} (curr) = {new_hrs_total} (total)")
            
        except Exception as e:
            logging.error(f"Error recalculating totals: {e}")
    
    def delete_equipment(self):
        """Delete selected equipment."""
        equipment_id = self.id_field.text()
        
        if not equipment_id:
            return
        
        # Confirm deletion
        confirm = QMessageBox.question(
            self,
            "Confirm Deletion",
            "Are you sure you want to delete this equipment? This will also delete all related fluids, maintenance records, etc.",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if confirm == QMessageBox.StandardButton.Yes:
            try:
                # Delete equipment
                Equipment.delete(int(equipment_id))
                self.load_data()
                self.clear_form()
                self.edit_button.setEnabled(False)
                self.delete_button.setEnabled(False)
                
                # Small delay to ensure database changes are committed, then refresh dependent tabs
                from PyQt5.QtCore import QTimer
                QTimer.singleShot(200, self.refresh_dependent_tabs)
            except Exception as e:
                logger.error(f"Error deleting equipment: {e}")
                QMessageBox.critical(
                    self,
                    "Error",
                    f"An error occurred: {str(e)}",
                    QMessageBox.StandardButton.Ok
                )

    def delete_all_equipment(self):
        """Delete all equipment records after confirmation."""
        confirm = QMessageBox.warning(
            self,
            "Delete ALL Equipment Records",
            "This will permanently delete ALL equipment and ALL related records in the system:\n\n"
            "• All Equipment data\n"
            "• All Fluids data\n"
            "• All Maintenance records\n"
            "• All Medium Reset (MR 1, MR 2) data\n"
            "• All Overhaul records\n"
            "• All Repair records\n"
            "• All Discard Criteria\n"
            "• All Tyre Maintenance\n"
            "• All Battery records\n"
            "• All Demand Forecasts\n"
            "• All other related data\n\n"
            "This action cannot be undone. Are you sure you wish to proceed?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        if confirm == QMessageBox.StandardButton.Yes:
            try:
                import database
                logger.info("Starting complete database cleanup...")

                # Disable foreign key constraints temporarily to avoid constraint errors
                connection = database.get_db_connection()
                if connection:
                    cursor = connection.cursor()

                    # Disable foreign key constraints
                    cursor.execute("PRAGMA foreign_keys = OFF")

                    # Delete all data in the correct order (child tables first, then parent)
                    tables_to_clear = [
                        'demand_forecast',
                        'tyre_forecast',
                        'battery_forecast',
                        'equipment_forecast',
                        'conditioning',
                        'maintenance_archives',
                        'medium_resets',
                        'overhauls',
                        'repairs',
                        'maintenance',
                        'tyre_maintenance',
                        'discard_criteria',
                        'discrepancies',
                        'battery',
                        'fluids',
                        'equipment'
                    ]

                    for table in tables_to_clear:
                        try:
                            cursor.execute(f"DELETE FROM {table}")
                            logger.info(f"Cleared table: {table}")
                        except Exception as table_error:
                            logger.warning(f"Could not clear table {table}: {table_error}")
                            # Continue with other tables even if one fails

                    # Re-enable foreign key constraints
                    cursor.execute("PRAGMA foreign_keys = ON")

                    # Commit all changes
                    connection.commit()
                    connection.close()

                    logger.info("Database cleanup completed successfully")

                    # Refresh UI
                    self.load_data()
                    self.clear_form()
                    self.edit_button.setEnabled(False)
                    self.delete_button.setEnabled(False)
                    self.send_to_maintenance_button.setEnabled(False)

                    # Small delay to ensure database changes are committed, then refresh dependent tabs
                    from PyQt5.QtCore import QTimer
                    QTimer.singleShot(200, self.refresh_dependent_tabs)

                    QMessageBox.information(
                        self,
                        "All Equipment Deleted",
                        "All equipment and related records have been successfully deleted from the system.",
                        QMessageBox.StandardButton.Ok
                    )
                else:
                    raise Exception("Could not establish database connection")

            except Exception as e:
                logger.error(f"Error deleting all equipment: {e}")
                QMessageBox.critical(
                    self,
                    "Error",
                    f"An error occurred while deleting all equipment:\n\n{str(e)}\n\nPlease check the application logs for more details.",
                    QMessageBox.StandardButton.Ok
                )
    
    def refresh_dependent_tabs(self):
        """Refresh all tabs that depend on equipment data."""
        try:
            # Get the main window
            main_window = self.window()
            logger.info(f"Refreshing dependent tabs. Main window type: {type(main_window).__name__}")
            
            # Find and refresh the Medium Reset widget
            if hasattr(main_window, 'medium_reset_widget'):
                logger.info("Refreshing Medium Reset tabs after equipment deletion")
                # Use force_refresh for more thorough update
                if hasattr(main_window.medium_reset_widget, 'force_refresh'):
                    logger.info("Using force_refresh method")
                    main_window.medium_reset_widget.force_refresh()
                else:
                    logger.info("Using load_data method")
                    main_window.medium_reset_widget.load_data()
                logger.info("Medium Reset refresh completed")
            else:
                logger.warning("Medium Reset widget not found in main window")
            
            # Find and refresh other dependent tabs
            if hasattr(main_window, 'fluids_widget'):
                logger.info("Refreshing Fluids tab after equipment deletion")
                main_window.fluids_widget.load_data()
            
            if hasattr(main_window, 'maintenance_widget'):
                logger.info("Refreshing Maintenance tab after equipment deletion")
                main_window.maintenance_widget.load_data()
                
            if hasattr(main_window, 'overhaul_widget'):
                logger.info("Refreshing Overhaul tab after equipment deletion")
                main_window.overhaul_widget.load_data()
                
            if hasattr(main_window, 'discard_criteria_widget'):
                logger.info("Refreshing Discard Criteria tab after equipment deletion")
                main_window.discard_criteria_widget.load_data()
                
            if hasattr(main_window, 'tyre_maintenance_widget'):
                logger.info("Refreshing Conditioning tab after equipment deletion")
                main_window.tyre_maintenance_widget.load_data()
                
            logger.info("All dependent tabs refreshed successfully")
            
        except Exception as e:
            logger.error(f"Error refreshing dependent tabs: {e}")
            # Don't show error to user as this is a nice-to-have feature
    
    def cancel_edit(self):
        """Cancel equipment edit."""
        # Get current equipment ID
        equipment_id = self.id_field.text()
        
        if equipment_id:
            # Reload equipment details
            self.load_equipment_details(int(equipment_id))
        else:
            # Clear form
            self.clear_form()
        
        # Disable form fields
        self.set_form_enabled(False)
    
    def send_to_maintenance(self):
        """Send selected equipment to maintenance tab."""
        equipment_id = self.id_field.text()
        
        if not equipment_id:
            QMessageBox.warning(
                self,
                "No Selection",
                "Please select an equipment first.",
                QMessageBox.StandardButton.Ok
            )
            return
        
        try:
            # Import the dialog here to avoid circular imports
            from ui.dialogs import MaintenanceTypeSelectionDialog
            
            # Get equipment data for the dialog
            equipment = Equipment.get_by_id(int(equipment_id))
            if not equipment:
                QMessageBox.critical(
                    self,
                    "Error",
                    "Selected equipment not found.",
                    QMessageBox.StandardButton.Ok
                )
                return
            
            # Show maintenance type selection dialog
            dialog = MaintenanceTypeSelectionDialog(equipment, self)
            if dialog.exec_() == dialog.Accepted:
                maintenance_data = dialog.get_maintenance_data()
                
                # Get the main window and switch to maintenance tab
                main_window = self.window()  # Get the main window
                if hasattr(main_window, 'switch_to_maintenance_tab'):
                    main_window.switch_to_maintenance_tab(
                        equipment_id=int(equipment_id),
                        maintenance_category=maintenance_data['category']
                    )
                else:
                    QMessageBox.information(
                        self,
                        "Success",
                        f"Maintenance record will be created for {maintenance_data['category']}",
                        QMessageBox.StandardButton.Ok
                    )
                    
        except Exception as e:
            logger.error(f"Error sending equipment to maintenance: {e}")
            QMessageBox.critical(
                self,
                "Error",
                f"Failed to send equipment to maintenance:\n{str(e)}",
                QMessageBox.StandardButton.Ok
            )

    def clear_data(self):
        """Clear all displayed data."""
        # Clear summary
        for label in [self.summary_ba_number, self.summary_make_type, self.summary_vintage,
                      self.summary_meterage, self.summary_hours, self.summary_status]:
            label.setText("--")
            
        # Clear statistics
        for label in [self.stats_monthly_km, self.stats_monthly_hrs, 
                      self.stats_efficiency, self.stats_utilization]:
            label.setText("--")
            
        # Clear maintenance
        for label in [self.maintenance_completed, self.maintenance_pending, self.maintenance_overdue]:
            label.setText("--")
            
        # Clear discard
        for label in [self.discard_status, self.discard_criteria_years, 
                      self.discard_criteria_kms, self.discard_remaining]:
            label.setText("--")
            
        # Clear tables
        self.maintenance_table.setRowCount(0)
        self.fluids_table.setRowCount(0)
        self.demand_table.setRowCount(0)
        self.mr_table.setRowCount(0)
        self.overhaul_table.setRowCount(0)