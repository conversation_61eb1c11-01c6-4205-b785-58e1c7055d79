"""Dialogs for adding, editing and viewing discard policies.
Currently minimal implementation to unblock UI; can be enhanced later.
"""
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QLabel, QComboBox,
    QSpinBox, QDateEdit, QCheckBox, QPushButton, QMessageBox
)
from PyQt5.QtCore import Qt, QDate
import models


class PolicyFormDialog(QDialog):
    """Dialog to create or edit a discard policy (checkbox style)."""

    def __init__(self, parent=None, equipment_id=None, policy=None):
        super().__init__(parent)
        self.policy = policy  # dict from DB when editing
        self.equipment_id = equipment_id
        self.setWindowTitle("Add Discard Policy" if policy is None else "Edit Discard Policy")
        self.resize(450, 400)
        self._build_ui()
        if policy:
            self._populate()

    # -------------------- UI helpers --------------------
    def _build_ui(self):
        layout = QVBoxLayout(self)

        form = QFormLayout()

        # Equipment selector
        self.equipment_combo = QComboBox()
        equip_list = models.Equipment.get_all() or []
        make_map = {}
        for e in equip_list:
            equip_id = e.get('EquipmentID') or e.get('equipment_id') or e.get('EquipmentId')
            if equip_id is None:
                continue  # skip malformed record
            make = (
                e.get('MakeAndType') or e.get('make_and_type') or e.get('make_type') or
                e.get('EquipmentType') or e.get('equipment_type') or e.get('type_name') or
                e.get('make') or ''
            )
            key = make.strip().lower() if make else str(equip_id)
            make_map.setdefault(key, {'label': make if make else str(equip_id), 'ids': []})['ids'].append(equip_id)
        for entry in make_map.values():
            self.equipment_combo.addItem(entry['label'], entry['ids'])
        if self.equipment_id:
            # preselect
            idx = self.equipment_combo.findData(self.equipment_id)
            if idx != -1:
                self.equipment_combo.setCurrentIndex(idx)
        form.addRow("Make & Type", self.equipment_combo)

        # Vintage Years
        self.chk_vintage = QCheckBox("Enable")
        self.spin_vintage = QSpinBox(); self.spin_vintage.setMaximum(99)
        self.spin_vintage.setEnabled(False)
        self.chk_vintage.toggled.connect(self.spin_vintage.setEnabled)
        h = QHBoxLayout(); h.addWidget(self.chk_vintage); h.addWidget(self.spin_vintage)
        form.addRow("Vintage Years", h)

        # Meterage Km
        self.chk_meter = QCheckBox("Enable")
        self.spin_meter = QSpinBox(); self.spin_meter.setMaximum(2_000_000); self.spin_meter.setEnabled(False)
        self.chk_meter.toggled.connect(self.spin_meter.setEnabled)
        h2 = QHBoxLayout(); h2.addWidget(self.chk_meter); h2.addWidget(self.spin_meter)
        form.addRow("Meterage (km)", h2)

        # OH1 Date
        self.chk_oh1 = QCheckBox("Enable")
        self.date_oh1 = QDateEdit(); self.date_oh1.setCalendarPopup(True); self.date_oh1.setEnabled(False); self.date_oh1.setDate(QDate.currentDate())
        self.chk_oh1.toggled.connect(self.date_oh1.setEnabled)
        h3 = QHBoxLayout(); h3.addWidget(self.chk_oh1); h3.addWidget(self.date_oh1)
        form.addRow("OH1 Done Date", h3)

        # OH2 Date
        self.chk_oh2 = QCheckBox("Enable")
        self.date_oh2 = QDateEdit(); self.date_oh2.setCalendarPopup(True); self.date_oh2.setEnabled(False); self.date_oh2.setDate(QDate.currentDate())
        self.chk_oh2.toggled.connect(self.date_oh2.setEnabled)
        h4 = QHBoxLayout(); h4.addWidget(self.chk_oh2); h4.addWidget(self.date_oh2)
        form.addRow("OH2 Done Date", h4)

        # MR1 Date
        self.chk_mr1 = QCheckBox("Enable")
        self.date_mr1 = QDateEdit(); self.date_mr1.setCalendarPopup(True); self.date_mr1.setEnabled(False); self.date_mr1.setDate(QDate.currentDate())
        self.chk_mr1.toggled.connect(self.date_mr1.setEnabled)
        h5 = QHBoxLayout(); h5.addWidget(self.chk_mr1); h5.addWidget(self.date_mr1)
        form.addRow("MR1 Done Date", h5)

        # MR2 Date
        self.chk_mr2 = QCheckBox("Enable")
        self.date_mr2 = QDateEdit(); self.date_mr2.setCalendarPopup(True); self.date_mr2.setEnabled(False); self.date_mr2.setDate(QDate.currentDate())
        self.chk_mr2.toggled.connect(self.date_mr2.setEnabled)
        h6 = QHBoxLayout(); h6.addWidget(self.chk_mr2); h6.addWidget(self.date_mr2)
        form.addRow("MR2 Done Date", h6)

        layout.addLayout(form)

        # Buttons
        btn_layout = QHBoxLayout()
        btn_layout.addStretch()
        save_btn = QPushButton("Save"); save_btn.clicked.connect(self._save)
        cancel_btn = QPushButton("Cancel"); cancel_btn.clicked.connect(self.reject)
        btn_layout.addWidget(cancel_btn); btn_layout.addWidget(save_btn)
        layout.addLayout(btn_layout)

    # -------------------- Data helpers --------------------
    def _populate(self):
        # TODO: populate based on self.policy dict when editing
        pass

    def _save(self):
        # Simple validation: at least one criterion enabled
        if not any([self.chk_vintage.isChecked(), self.chk_meter.isChecked(), self.chk_oh1.isChecked(),
                    self.chk_oh2.isChecked(), self.chk_mr1.isChecked(), self.chk_mr2.isChecked()]):
            QMessageBox.warning(self, "Validation", "Select at least one criterion.")
            return
        equip_ids = self.equipment_combo.currentData() or []
        if not isinstance(equip_ids, list):
            equip_ids = [equip_ids]
        if not equip_ids:
            QMessageBox.warning(self, "Validation", "No equipment found for the selected Make & Type")
            return

        # Fetch make_and_type from the first equipment row (all share same)
        eq_row = models.database.execute_query(
            "SELECT make_and_type FROM equipment WHERE equipment_id = %s",
            (equip_ids[0],), fetchall=False,
        )
        make_type = (eq_row or {}).get("make_and_type") or "Unknown"

        # Ensure discard_policy_class exists for this make_and_type
        cls = models.database.execute_query(
            "SELECT class_id FROM discard_policy_class WHERE class_name = %s",
            (make_type,), fetchall=False,
        )
        if not cls:
            cls_id = models.DiscardPolicyClass(class_name=make_type, description="Auto-created").save()
        else:
            cls_id = cls["class_id"]

        data = {
            'vintage_years': self.spin_vintage.value() if self.chk_vintage.isChecked() else None,
            'meterage_km': self.spin_meter.value() if self.chk_meter.isChecked() else None,
            # date-based fields would be stored in `conditions` JSON for now
        }

        # Remove any pre-existing class-level policy to avoid duplicates
        models.database.execute_query(
            "UPDATE discard_policy SET active = 0 WHERE class_id = %s AND equipment_id IS NULL",
            (cls_id,), fetchall=False,
        )

        # Soft-delete earlier per-equipment policies for this make/type (cleanup)
        placeholders = ",".join(["?"] * len(equip_ids))
        models.database.execute_query(
            f"UPDATE discard_policy SET active = 0 WHERE equipment_id IN ({placeholders})",
            tuple(equip_ids), fetchall=False,
        )

        # Insert single class-level policy
        models.DiscardPolicy(
            class_id=cls_id,
            equipment_id=None,
            vintage_years=data['vintage_years'],
            first_oh_kms=data['meterage_km'],
            discard_criteria=None,
            conditions=None,
            special_conditions=None,
        ).save()
        self.accept()


class PolicyViewDialog(QDialog):
    """Read-only view of existing discard policy."""

    def __init__(self, parent=None, equipment_id=None):
        super().__init__(parent)
        self.setWindowTitle("View Discard Policy")
        self.resize(450, 400)
        self._build_ui()
        self._load(equipment_id)

    def _build_ui(self):
        layout = QVBoxLayout(self)
        self.info_label = QLabel("Loading policy…")
        self.info_label.setWordWrap(True)
        layout.addWidget(self.info_label)
        close_btn = QPushButton("Close"); close_btn.clicked.connect(self.accept)
        h = QHBoxLayout(); h.addStretch(); h.addWidget(close_btn)
        layout.addLayout(h)

    def _load(self, equipment_id):
        if not equipment_id:
            self.info_label.setText("No equipment selected.")
            return
        policies = models.DiscardPolicy.get_all(active_only=True) or []
        pol = next((p for p in policies if p.get('equipment_id') == equipment_id), None)
        if not pol:
            self.info_label.setText("No active policy for this equipment.")
            return
        # Build readable text
        txt = []
        txt.append(f"Equipment ID: {equipment_id}")
        txt.append(f"Vintage Years: {pol.get('vintage_years') or '--'}")
        txt.append(f"Meterage (km): {pol.get('meterage_km') or '--'}")
        txt.append(f"OH1 Date: {pol.get('oh1_date') or '--'}")
        txt.append(f"OH2 Date: {pol.get('oh2_date') or '--'}")
        txt.append(f"MR1 Date: {pol.get('mr1_date') or '--'}")
        txt.append(f"MR2 Date: {pol.get('mr2_date') or '--'}")
        self.info_label.setText('\n'.join(txt))
