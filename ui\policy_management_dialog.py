"""
Policy Management Dialog

Comprehensive interface for managing discard policies through the UI.
Allows users to create, edit, and delete discard policies for equipment types.
"""

import logging
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                           QPushButton, QTableWidget, QTableWidgetItem,
                           QHeaderView, QAbstractItemView, QMessageBox,
                           QFormLayout, QGroupBox, QLineEdit, QTextEdit,
                           QCheckBox, QSpinBox, QDoubleSpinBox, QComboBox,
                           QSplitter, QFrame, QTabWidget, QWidget)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QColor, QFont

import models
import database
from ui.common_styles import *

logger = logging.getLogger(__name__)


class PolicyManagementDialog(QDialog):
    """Dialog for comprehensive policy management."""
    
    # Signal emitted when policies are updated
    policies_updated = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Discard Policy Management")
        self.setModal(True)
        self.resize(1000, 700)
        
        # Current policy being edited
        self.current_policy_id = None
        self.current_class_id = None
        
        self.setup_ui()
        self.load_data()
        
    def setup_ui(self):
        """Set up the user interface."""
        layout = QVBoxLayout(self)
        
        # Title
        title_label = QLabel("Discard Policy Management")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # Main content with tabs
        tab_widget = QTabWidget()
        
        # Equipment Classes Tab
        classes_tab = self.create_classes_tab()
        tab_widget.addTab(classes_tab, "Equipment Classes")
        
        # Discard Policies Tab
        policies_tab = self.create_policies_tab()
        tab_widget.addTab(policies_tab, "Discard Policies")
        
        layout.addWidget(tab_widget)
        
        # Bottom buttons
        button_layout = QHBoxLayout()
        
        self.refresh_button = QPushButton("Refresh Data")
        self.refresh_button.clicked.connect(self.load_data)
        apply_button_style(self.refresh_button, "default")
        button_layout.addWidget(self.refresh_button)
        
        button_layout.addStretch()
        
        self.close_button = QPushButton("Close")
        self.close_button.clicked.connect(self.accept)
        apply_button_style(self.close_button, "primary")
        button_layout.addWidget(self.close_button)
        
        layout.addLayout(button_layout)
        
        # Apply styling
        self.setStyleSheet(get_complete_stylesheet())
    
    def create_classes_tab(self):
        """Create the equipment classes management tab."""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        
        # Left side - Classes list
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        
        # Classes table
        classes_label = QLabel("Equipment Classes")
        classes_label.setStyleSheet("font-weight: bold; font-size: 12px;")
        left_layout.addWidget(classes_label)
        
        self.classes_table = QTableWidget()
        self.classes_table.setColumnCount(3)
        self.classes_table.setHorizontalHeaderLabels(["Class Name", "Description", "Policies"])
        self.classes_table.verticalHeader().setVisible(False)
        self.classes_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.classes_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.classes_table.itemSelectionChanged.connect(self.class_selected)
        
        # Set column widths
        header = self.classes_table.horizontalHeader()
        header.resizeSection(0, 200)
        header.resizeSection(1, 300)
        header.resizeSection(2, 80)
        
        left_layout.addWidget(self.classes_table)
        
        # Class management buttons
        class_button_layout = QHBoxLayout()
        
        self.add_class_button = QPushButton("Add Class")
        self.add_class_button.clicked.connect(self.add_equipment_class)
        apply_button_style(self.add_class_button, "success")
        class_button_layout.addWidget(self.add_class_button)
        
        self.edit_class_button = QPushButton("Edit Class")
        self.edit_class_button.clicked.connect(self.edit_equipment_class)
        self.edit_class_button.setEnabled(False)
        apply_button_style(self.edit_class_button, "default")
        class_button_layout.addWidget(self.edit_class_button)
        
        self.delete_class_button = QPushButton("Delete Class")
        self.delete_class_button.clicked.connect(self.delete_equipment_class)
        self.delete_class_button.setEnabled(False)
        apply_button_style(self.delete_class_button, "danger")
        class_button_layout.addWidget(self.delete_class_button)
        
        class_button_layout.addStretch()
        left_layout.addLayout(class_button_layout)
        
        # Right side - Class details
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        
        # Class details form
        details_group = QGroupBox("Class Details")
        details_layout = QFormLayout(details_group)
        
        self.class_name_edit = QLineEdit()
        self.class_name_edit.setPlaceholderText("Enter equipment class name...")
        details_layout.addRow("Class Name:", self.class_name_edit)
        
        self.class_description_edit = QTextEdit()
        self.class_description_edit.setPlaceholderText("Enter description...")
        self.class_description_edit.setMaximumHeight(100)
        details_layout.addRow("Description:", self.class_description_edit)
        
        # Save class button
        self.save_class_button = QPushButton("Save Class")
        self.save_class_button.clicked.connect(self.save_equipment_class)
        self.save_class_button.setEnabled(False)
        apply_button_style(self.save_class_button, "primary")
        details_layout.addRow(self.save_class_button)
        
        right_layout.addWidget(details_group)
        right_layout.addStretch()
        
        # Add to splitter
        splitter = QSplitter(Qt.Horizontal)
        splitter.addWidget(left_widget)
        splitter.addWidget(right_widget)
        splitter.setSizes([600, 400])
        
        layout.addWidget(splitter)
        return widget
    
    def create_policies_tab(self):
        """Create the discard policies management tab."""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        
        # Left side - Policies list
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        
        # Policies table
        policies_label = QLabel("Discard Policies")
        policies_label.setStyleSheet("font-weight: bold; font-size: 12px;")
        left_layout.addWidget(policies_label)
        
        self.policies_table = QTableWidget()
        self.policies_table.setColumnCount(6)
        self.policies_table.setHorizontalHeaderLabels([
            "Equipment Class", "Vintage Years", "Mileage (KM)", "Hours", "Active", "Special Conditions"
        ])
        self.policies_table.verticalHeader().setVisible(False)
        self.policies_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.policies_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.policies_table.itemSelectionChanged.connect(self.policy_selected)
        
        # Set column widths
        header = self.policies_table.horizontalHeader()
        header.resizeSection(0, 150)
        header.resizeSection(1, 80)
        header.resizeSection(2, 80)
        header.resizeSection(3, 60)
        header.resizeSection(4, 60)
        header.resizeSection(5, 200)
        
        left_layout.addWidget(self.policies_table)
        
        # Policy management buttons
        policy_button_layout = QHBoxLayout()
        
        self.add_policy_button = QPushButton("Add Policy")
        self.add_policy_button.clicked.connect(self.add_discard_policy)
        apply_button_style(self.add_policy_button, "success")
        policy_button_layout.addWidget(self.add_policy_button)
        
        self.edit_policy_button = QPushButton("Edit Policy")
        self.edit_policy_button.clicked.connect(self.edit_discard_policy)
        self.edit_policy_button.setEnabled(False)
        apply_button_style(self.edit_policy_button, "default")
        policy_button_layout.addWidget(self.edit_policy_button)
        
        self.delete_policy_button = QPushButton("Delete Policy")
        self.delete_policy_button.clicked.connect(self.delete_discard_policy)
        self.delete_policy_button.setEnabled(False)
        apply_button_style(self.delete_policy_button, "danger")
        policy_button_layout.addWidget(self.delete_policy_button)
        
        policy_button_layout.addStretch()
        left_layout.addLayout(policy_button_layout)
        
        # Right side - Policy details
        right_widget = self.create_policy_form()
        
        # Add to splitter
        splitter = QSplitter(Qt.Horizontal)
        splitter.addWidget(left_widget)
        splitter.addWidget(right_widget)
        splitter.setSizes([600, 400])
        
        layout.addWidget(splitter)
        return widget

    def create_policy_form(self):
        """Create the policy editing form."""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Policy details form
        details_group = QGroupBox("Policy Details")
        details_layout = QFormLayout(details_group)

        # Equipment class selection
        self.policy_class_combo = QComboBox()
        self.policy_class_combo.setEditable(True)
        details_layout.addRow("Equipment Class:", self.policy_class_combo)

        # Discard criteria
        criteria_frame = QFrame()
        criteria_layout = QFormLayout(criteria_frame)

        self.vintage_years_spin = QSpinBox()
        self.vintage_years_spin.setRange(0, 100)
        self.vintage_years_spin.setSuffix(" years")
        self.vintage_years_spin.setSpecialValueText("Not Set")
        criteria_layout.addRow("Vintage Years:", self.vintage_years_spin)

        self.discard_kms_spin = QSpinBox()
        self.discard_kms_spin.setRange(0, 9999999)
        self.discard_kms_spin.setSuffix(" km")
        self.discard_kms_spin.setSpecialValueText("Not Set")
        criteria_layout.addRow("Discard Mileage:", self.discard_kms_spin)

        self.hours_spin = QSpinBox()
        self.hours_spin.setRange(0, 99999)
        self.hours_spin.setSuffix(" hours")
        self.hours_spin.setSpecialValueText("Not Set")
        criteria_layout.addRow("Hours Limit:", self.hours_spin)

        details_layout.addRow("Discard Criteria:", criteria_frame)

        # Overhaul criteria
        overhaul_frame = QFrame()
        overhaul_layout = QFormLayout(overhaul_frame)

        self.first_oh_years_spin = QSpinBox()
        self.first_oh_years_spin.setRange(0, 100)
        self.first_oh_years_spin.setSuffix(" years")
        self.first_oh_years_spin.setSpecialValueText("Not Set")
        overhaul_layout.addRow("First OH Years:", self.first_oh_years_spin)

        self.first_oh_kms_spin = QSpinBox()
        self.first_oh_kms_spin.setRange(0, 9999999)
        self.first_oh_kms_spin.setSuffix(" km")
        self.first_oh_kms_spin.setSpecialValueText("Not Set")
        overhaul_layout.addRow("First OH Mileage:", self.first_oh_kms_spin)

        details_layout.addRow("Overhaul Criteria:", overhaul_frame)

        # Special conditions
        self.special_conditions_edit = QTextEdit()
        self.special_conditions_edit.setPlaceholderText("Enter special conditions, notes, or complex rules...")
        self.special_conditions_edit.setMaximumHeight(80)
        details_layout.addRow("Special Conditions:", self.special_conditions_edit)

        # Active status
        self.active_checkbox = QCheckBox("Policy Active")
        self.active_checkbox.setChecked(True)
        details_layout.addRow(self.active_checkbox)

        # Save policy button
        self.save_policy_button = QPushButton("Save Policy")
        self.save_policy_button.clicked.connect(self.save_discard_policy)
        self.save_policy_button.setEnabled(False)
        apply_button_style(self.save_policy_button, "primary")
        details_layout.addRow(self.save_policy_button)

        layout.addWidget(details_group)

        # Policy preview
        preview_group = QGroupBox("Policy Preview")
        preview_layout = QVBoxLayout(preview_group)

        self.policy_preview_label = QLabel("Select or create a policy to see preview")
        self.policy_preview_label.setStyleSheet("color: #666; font-style: italic; padding: 10px;")
        self.policy_preview_label.setWordWrap(True)
        preview_layout.addWidget(self.policy_preview_label)

        layout.addWidget(preview_group)
        layout.addStretch()

        return widget

    def load_data(self):
        """Load equipment classes and policies data."""
        self.load_equipment_classes()
        self.load_discard_policies()
        self.load_class_combo()

    def load_equipment_classes(self):
        """Load equipment classes into the table."""
        try:
            self.classes_table.setRowCount(0)

            classes = database.execute_query("""
                SELECT dpc.*,
                       COUNT(dp.policy_id) as policy_count
                FROM discard_policy_class dpc
                LEFT JOIN discard_policy dp ON dpc.class_id = dp.class_id AND dp.active = 1
                GROUP BY dpc.class_id, dpc.class_name, dpc.description
                ORDER BY dpc.class_name
            """)

            for class_data in classes:
                row = self.classes_table.rowCount()
                self.classes_table.insertRow(row)

                # Class name
                name_item = QTableWidgetItem(class_data['class_name'])
                name_item.setData(Qt.UserRole, class_data['class_id'])
                self.classes_table.setItem(row, 0, name_item)

                # Description
                description = class_data.get('description') or ''
                desc_item = QTableWidgetItem(description[:50] + '...' if len(description) > 50 else description)
                desc_item.setToolTip(description)
                self.classes_table.setItem(row, 1, desc_item)

                # Policy count
                policy_count = class_data.get('policy_count', 0)
                count_item = QTableWidgetItem(str(policy_count))
                count_item.setTextAlignment(Qt.AlignCenter)
                self.classes_table.setItem(row, 2, count_item)

            self.classes_table.resizeColumnsToContents()

        except Exception as e:
            logger.error(f"Error loading equipment classes: {e}")
            QMessageBox.critical(self, "Error", f"Failed to load equipment classes: {e}")

    def load_discard_policies(self):
        """Load discard policies into the table."""
        try:
            self.policies_table.setRowCount(0)

            policies = database.execute_query("""
                SELECT dp.*, dpc.class_name
                FROM discard_policy dp
                JOIN discard_policy_class dpc ON dp.class_id = dpc.class_id
                WHERE dp.equipment_id IS NULL
                ORDER BY dpc.class_name, dp.policy_id
            """)

            for policy in policies:
                row = self.policies_table.rowCount()
                self.policies_table.insertRow(row)

                # Equipment class
                class_item = QTableWidgetItem(policy['class_name'])
                class_item.setData(Qt.UserRole, policy['policy_id'])
                class_item.setData(Qt.UserRole + 1, policy['class_id'])
                self.policies_table.setItem(row, 0, class_item)

                # Vintage years
                vintage_years = policy.get('vintage_years') or policy.get('discard_years')
                years_text = str(vintage_years) if vintage_years else "Not Set"
                self.policies_table.setItem(row, 1, QTableWidgetItem(years_text))

                # Mileage
                mileage = policy.get('first_oh_kms') or policy.get('second_oh_kms')
                mileage_text = str(mileage) if mileage else "Not Set"
                self.policies_table.setItem(row, 2, QTableWidgetItem(mileage_text))

                # Hours (from special conditions)
                hours_text = "Not Set"
                special_conditions = policy.get('special_conditions', '')
                if special_conditions and 'HRS' in special_conditions.upper():
                    import re
                    hours_match = re.search(r'(\d+)\s*HRS', special_conditions.upper())
                    if hours_match:
                        hours_text = hours_match.group(1)
                self.policies_table.setItem(row, 3, QTableWidgetItem(hours_text))

                # Active status
                active = policy.get('active', 1)
                active_item = QTableWidgetItem("Yes" if active else "No")
                if not active:
                    active_item.setBackground(QColor("#ffebee"))
                self.policies_table.setItem(row, 4, active_item)

                # Special conditions (truncated)
                conditions = special_conditions[:30] + '...' if len(special_conditions) > 30 else special_conditions
                conditions_item = QTableWidgetItem(conditions)
                conditions_item.setToolTip(special_conditions)
                self.policies_table.setItem(row, 5, conditions_item)

            self.policies_table.resizeColumnsToContents()

        except Exception as e:
            logger.error(f"Error loading discard policies: {e}")
            QMessageBox.critical(self, "Error", f"Failed to load discard policies: {e}")

    def load_class_combo(self):
        """Load equipment classes into the combo box."""
        try:
            self.policy_class_combo.clear()

            classes = database.execute_query("""
                SELECT class_id, class_name
                FROM discard_policy_class
                ORDER BY class_name
            """)

            for class_data in classes:
                self.policy_class_combo.addItem(
                    class_data['class_name'],
                    class_data['class_id']
                )

        except Exception as e:
            logger.error(f"Error loading class combo: {e}")

    def class_selected(self):
        """Handle equipment class selection."""
        selected_items = self.classes_table.selectedItems()
        if not selected_items:
            self.edit_class_button.setEnabled(False)
            self.delete_class_button.setEnabled(False)
            self.save_class_button.setEnabled(False)
            self.clear_class_form()
            return

        # Enable buttons
        self.edit_class_button.setEnabled(True)
        self.delete_class_button.setEnabled(True)
        self.save_class_button.setEnabled(True)

        # Get class data
        row = selected_items[0].row()
        class_id = self.classes_table.item(row, 0).data(Qt.UserRole)

        try:
            class_data = database.execute_query(
                "SELECT * FROM discard_policy_class WHERE class_id = ?",
                (class_id,), fetchall=False
            )

            if class_data:
                self.current_class_id = class_id
                self.class_name_edit.setText(class_data['class_name'])
                self.class_description_edit.setPlainText(class_data.get('description') or '')

        except Exception as e:
            logger.error(f"Error loading class details: {e}")

    def policy_selected(self):
        """Handle discard policy selection."""
        selected_items = self.policies_table.selectedItems()
        if not selected_items:
            self.edit_policy_button.setEnabled(False)
            self.delete_policy_button.setEnabled(False)
            self.save_policy_button.setEnabled(False)
            self.clear_policy_form()
            return

        # Enable buttons
        self.edit_policy_button.setEnabled(True)
        self.delete_policy_button.setEnabled(True)
        self.save_policy_button.setEnabled(True)

        # Get policy data
        row = selected_items[0].row()
        policy_id = self.policies_table.item(row, 0).data(Qt.UserRole)
        class_id = self.policies_table.item(row, 0).data(Qt.UserRole + 1)

        try:
            policy_data = database.execute_query(
                "SELECT * FROM discard_policy WHERE policy_id = ?",
                (policy_id,), fetchall=False
            )

            if policy_data:
                self.current_policy_id = policy_id
                self.load_policy_form(policy_data)
                self.update_policy_preview(policy_data)

        except Exception as e:
            logger.error(f"Error loading policy details: {e}")

    def load_policy_form(self, policy_data):
        """Load policy data into the form."""
        # Set class combo
        class_id = policy_data['class_id']
        for i in range(self.policy_class_combo.count()):
            if self.policy_class_combo.itemData(i) == class_id:
                self.policy_class_combo.setCurrentIndex(i)
                break

        # Set criteria values
        vintage_years = policy_data.get('vintage_years') or policy_data.get('discard_years') or 0
        self.vintage_years_spin.setValue(vintage_years)

        discard_kms = policy_data.get('first_oh_kms') or policy_data.get('second_oh_kms') or 0
        self.discard_kms_spin.setValue(discard_kms)

        first_oh_years = policy_data.get('first_oh_years') or 0
        self.first_oh_years_spin.setValue(first_oh_years)

        first_oh_kms = policy_data.get('first_oh_kms') or 0
        self.first_oh_kms_spin.setValue(first_oh_kms)

        # Extract hours from special conditions
        special_conditions = policy_data.get('special_conditions', '')
        hours = 0
        if special_conditions and 'HRS' in special_conditions.upper():
            import re
            hours_match = re.search(r'(\d+)\s*HRS', special_conditions.upper())
            if hours_match:
                hours = int(hours_match.group(1))
        self.hours_spin.setValue(hours)

        self.special_conditions_edit.setPlainText(special_conditions)
        self.active_checkbox.setChecked(bool(policy_data.get('active', 1)))

    def update_policy_preview(self, policy_data):
        """Update the policy preview text."""
        try:
            class_name = database.execute_query(
                "SELECT class_name FROM discard_policy_class WHERE class_id = ?",
                (policy_data['class_id'],), fetchall=False
            )['class_name']

            preview_text = f"Policy for: {class_name}\n\n"

            vintage_years = policy_data.get('vintage_years') or policy_data.get('discard_years')
            if vintage_years:
                preview_text += f"• Discard after {vintage_years} years\n"

            mileage = policy_data.get('first_oh_kms') or policy_data.get('second_oh_kms')
            if mileage:
                preview_text += f"• Discard after {mileage:,} km\n"

            special_conditions = policy_data.get('special_conditions', '')
            if special_conditions and 'HRS' in special_conditions.upper():
                import re
                hours_match = re.search(r'(\d+)\s*HRS', special_conditions.upper())
                if hours_match:
                    preview_text += f"• Discard after {hours_match.group(1)} hours\n"

            if special_conditions:
                preview_text += f"\nSpecial Conditions:\n{special_conditions}"

            active = policy_data.get('active', 1)
            status = "Active" if active else "Inactive"
            preview_text += f"\n\nStatus: {status}"

            self.policy_preview_label.setText(preview_text)

        except Exception as e:
            logger.error(f"Error updating policy preview: {e}")
            self.policy_preview_label.setText("Error loading policy preview")

    def clear_class_form(self):
        """Clear the class editing form."""
        self.current_class_id = None
        self.class_name_edit.clear()
        self.class_description_edit.clear()

    def clear_policy_form(self):
        """Clear the policy editing form."""
        self.current_policy_id = None
        self.policy_class_combo.setCurrentIndex(0)
        self.vintage_years_spin.setValue(0)
        self.discard_kms_spin.setValue(0)
        self.hours_spin.setValue(0)
        self.first_oh_years_spin.setValue(0)
        self.first_oh_kms_spin.setValue(0)
        self.special_conditions_edit.clear()
        self.active_checkbox.setChecked(True)
        self.policy_preview_label.setText("Select or create a policy to see preview")

    # Equipment Class CRUD Operations
    def add_equipment_class(self):
        """Add a new equipment class."""
        self.clear_class_form()
        self.save_class_button.setEnabled(True)
        self.class_name_edit.setFocus()

    def edit_equipment_class(self):
        """Edit the selected equipment class."""
        # Form is already loaded by class_selected()
        self.class_name_edit.setFocus()

    def save_equipment_class(self):
        """Save the equipment class."""
        class_name = self.class_name_edit.text().strip()
        description = self.class_description_edit.toPlainText().strip()

        if not class_name:
            QMessageBox.warning(self, "Validation Error", "Class name is required.")
            return

        try:
            if self.current_class_id:
                # Update existing class
                database.execute_query("""
                    UPDATE discard_policy_class
                    SET class_name = ?, description = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE class_id = ?
                """, (class_name, description, self.current_class_id))
                message = "Equipment class updated successfully."
            else:
                # Create new class
                database.execute_query("""
                    INSERT INTO discard_policy_class (class_name, description)
                    VALUES (?, ?)
                """, (class_name, description))
                message = "Equipment class created successfully."

            self.load_data()
            self.clear_class_form()
            self.save_class_button.setEnabled(False)
            QMessageBox.information(self, "Success", message)

        except Exception as e:
            logger.error(f"Error saving equipment class: {e}")
            QMessageBox.critical(self, "Error", f"Failed to save equipment class: {e}")

    def delete_equipment_class(self):
        """Delete the selected equipment class."""
        if not self.current_class_id:
            return

        # Check if class has policies
        policies = database.execute_query(
            "SELECT COUNT(*) as count FROM discard_policy WHERE class_id = ?",
            (self.current_class_id,)
        )

        policy_count = policies[0]['count'] if policies else 0

        if policy_count > 0:
            reply = QMessageBox.question(
                self, "Confirm Deletion",
                f"This equipment class has {policy_count} associated policies. "
                "Deleting the class will also delete all its policies. Continue?",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
        else:
            reply = QMessageBox.question(
                self, "Confirm Deletion",
                "Are you sure you want to delete this equipment class?",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

        if reply == QMessageBox.Yes:
            try:
                # Delete policies first
                database.execute_query(
                    "DELETE FROM discard_policy WHERE class_id = ?",
                    (self.current_class_id,)
                )

                # Delete class
                database.execute_query(
                    "DELETE FROM discard_policy_class WHERE class_id = ?",
                    (self.current_class_id,)
                )

                self.load_data()
                self.clear_class_form()
                self.edit_class_button.setEnabled(False)
                self.delete_class_button.setEnabled(False)
                self.save_class_button.setEnabled(False)

                QMessageBox.information(self, "Success", "Equipment class deleted successfully.")

            except Exception as e:
                logger.error(f"Error deleting equipment class: {e}")
                QMessageBox.critical(self, "Error", f"Failed to delete equipment class: {e}")

    # Discard Policy CRUD Operations
    def add_discard_policy(self):
        """Add a new discard policy."""
        self.clear_policy_form()
        self.save_policy_button.setEnabled(True)
        self.policy_class_combo.setFocus()

    def edit_discard_policy(self):
        """Edit the selected discard policy."""
        # Form is already loaded by policy_selected()
        self.policy_class_combo.setFocus()

    def save_discard_policy(self):
        """Save the discard policy."""
        class_id = self.policy_class_combo.currentData()
        class_name = self.policy_class_combo.currentText().strip()

        if not class_id and not class_name:
            QMessageBox.warning(self, "Validation Error", "Equipment class is required.")
            return

        # Get form values
        vintage_years = self.vintage_years_spin.value() if self.vintage_years_spin.value() > 0 else None
        discard_kms = self.discard_kms_spin.value() if self.discard_kms_spin.value() > 0 else None
        hours = self.hours_spin.value() if self.hours_spin.value() > 0 else None
        first_oh_years = self.first_oh_years_spin.value() if self.first_oh_years_spin.value() > 0 else None
        first_oh_kms = self.first_oh_kms_spin.value() if self.first_oh_kms_spin.value() > 0 else None
        special_conditions = self.special_conditions_edit.toPlainText().strip()
        active = 1 if self.active_checkbox.isChecked() else 0

        # Validation
        if not vintage_years and not discard_kms and not hours:
            QMessageBox.warning(self, "Validation Error",
                              "At least one discard criteria (Years, Mileage, or Hours) must be specified.")
            return

        # Add hours to special conditions if specified
        if hours and special_conditions:
            special_conditions += f" | Hours: {hours}"
        elif hours:
            special_conditions = f"Hours: {hours}"

        try:
            # Create class if it doesn't exist
            if not class_id:
                database.execute_query("""
                    INSERT INTO discard_policy_class (class_name, description)
                    VALUES (?, ?)
                """, (class_name, f"Auto-created class for {class_name}"))

                class_result = database.execute_query(
                    "SELECT class_id FROM discard_policy_class WHERE class_name = ?",
                    (class_name,), fetchall=False
                )
                class_id = class_result['class_id']

            if self.current_policy_id:
                # Update existing policy
                database.execute_query("""
                    UPDATE discard_policy
                    SET class_id = ?, vintage_years = ?, first_oh_kms = ?,
                        discard_years = ?, special_conditions = ?, active = ?,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE policy_id = ?
                """, (class_id, vintage_years, first_oh_kms, vintage_years,
                      special_conditions, active, self.current_policy_id))
                message = "Discard policy updated successfully."
            else:
                # Create new policy
                database.execute_query("""
                    INSERT INTO discard_policy
                    (class_id, equipment_id, vintage_years, first_oh_kms,
                     discard_years, special_conditions, active)
                    VALUES (?, NULL, ?, ?, ?, ?, ?)
                """, (class_id, vintage_years, first_oh_kms, vintage_years,
                      special_conditions, active))
                message = "Discard policy created successfully."

            self.load_data()
            self.clear_policy_form()
            self.save_policy_button.setEnabled(False)

            # Emit signal to update main widget
            self.policies_updated.emit()

            QMessageBox.information(self, "Success", message)

        except Exception as e:
            logger.error(f"Error saving discard policy: {e}")
            QMessageBox.critical(self, "Error", f"Failed to save discard policy: {e}")

    def delete_discard_policy(self):
        """Delete the selected discard policy."""
        if not self.current_policy_id:
            return

        reply = QMessageBox.question(
            self, "Confirm Deletion",
            "Are you sure you want to delete this discard policy?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                database.execute_query(
                    "DELETE FROM discard_policy WHERE policy_id = ?",
                    (self.current_policy_id,)
                )

                self.load_data()
                self.clear_policy_form()
                self.edit_policy_button.setEnabled(False)
                self.delete_policy_button.setEnabled(False)
                self.save_policy_button.setEnabled(False)

                # Emit signal to update main widget
                self.policies_updated.emit()

                QMessageBox.information(self, "Success", "Discard policy deleted successfully.")

            except Exception as e:
                logger.error(f"Error deleting discard policy: {e}")
                QMessageBox.critical(self, "Error", f"Failed to delete discard policy: {e}")
