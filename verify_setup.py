#!/usr/bin/env python3
"""
Verify Discard Policy Setup

Simple verification script to check if the discard policy system is working correctly.
"""

import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import database
import models

def main():
    """Main verification function."""
    print("=" * 50)
    print("DISCARD POLICY SYSTEM VERIFICATION")
    print("=" * 50)
    
    try:
        # Initialize database
        success, first_run = database.init_connection_pool()
        if not success:
            print("ERROR: Failed to initialize database")
            return False
        
        # Check tables exist
        tables = ['equipment', 'discard_policy_class', 'discard_policy']
        print("\n1. Checking database tables:")
        for table in tables:
            result = database.execute_query(
                "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
                (table,), fetchall=False
            )
            status = "EXISTS" if result else "MISSING"
            print(f"   {table}: {status}")
        
        # Count equipment classes
        print("\n2. Equipment Classes:")
        classes = database.execute_query("SELECT COUNT(*) as count FROM discard_policy_class")
        class_count = classes[0]['count'] if classes else 0
        print(f"   Total Classes: {class_count}")
        
        if class_count > 0:
            sample_classes = database.execute_query("SELECT class_name FROM discard_policy_class LIMIT 5")
            print("   Sample Classes:")
            for cls in sample_classes:
                print(f"     - {cls['class_name']}")
        
        # Count policies
        print("\n3. Discard Policies:")
        policies = database.execute_query("SELECT COUNT(*) as count FROM discard_policy WHERE active = 1")
        policy_count = policies[0]['count'] if policies else 0
        print(f"   Active Policies: {policy_count}")
        
        if policy_count > 0:
            sample_policies = database.execute_query("""
                SELECT dp.vintage_years, dp.first_oh_kms, dpc.class_name 
                FROM discard_policy dp
                JOIN discard_policy_class dpc ON dp.class_id = dpc.class_id
                WHERE dp.active = 1 AND dp.equipment_id IS NULL
                LIMIT 5
            """)
            print("   Sample Policies:")
            for policy in sample_policies:
                print(f"     - {policy['class_name']}: {policy['vintage_years']} years, {policy['first_oh_kms']} km")
        
        # Count equipment
        print("\n4. Equipment:")
        equipment = database.execute_query("SELECT COUNT(*) as count FROM equipment WHERE equipment_status != 'discarded' OR equipment_status IS NULL")
        equipment_count = equipment[0]['count'] if equipment else 0
        print(f"   Active Equipment: {equipment_count}")
        
        # Test policy service
        print("\n5. Testing Policy Service:")
        try:
            from policy_service import policy_service
            
            # Get some equipment to test
            equipment_list = models.Equipment.get_active()
            
            if equipment_list:
                test_equipment = equipment_list[0]
                equipment_id = test_equipment.get('equipment_id')
                make_type = test_equipment.get('make_and_type', 'Unknown')
                
                print(f"   Testing Equipment: {make_type}")
                
                status_info = policy_service.get_equipment_discard_status(equipment_id)
                print(f"   Status: {status_info.get('status')}")
                print(f"   Message: {status_info.get('message')}")
                
                # Test grouped listing
                grouped_equipment = policy_service.get_equipment_due_for_discard()
                print(f"   Equipment Groups Meeting Criteria: {len(grouped_equipment)}")
                
                print("   POLICY SERVICE: WORKING")
            else:
                print("   No equipment found for testing")
                
        except Exception as e:
            print(f"   POLICY SERVICE ERROR: {e}")
        
        # Test services integration
        print("\n6. Testing Services Integration:")
        try:
            from services import discard_service
            rows = discard_service.list_due()
            print(f"   Service returned {len(rows)} equipment groups")
            print("   SERVICES INTEGRATION: WORKING")
        except Exception as e:
            print(f"   SERVICES INTEGRATION ERROR: {e}")
        
        print("\n" + "=" * 50)
        print("VERIFICATION COMPLETED")
        print("=" * 50)
        print("\nSUMMARY:")
        print(f"- Equipment Classes: {class_count}")
        print(f"- Active Policies: {policy_count}")
        print(f"- Active Equipment: {equipment_count}")
        print("\nThe discard policy system is ready for use!")
        print("Run 'python main.py' and navigate to the 'Discard Criteria' tab.")
        
        return True
        
    except Exception as e:
        print(f"VERIFICATION FAILED: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
